import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import { RecipeStatus } from "../models/Recipe";
import { RecipeHistoryAction } from "../models/RecipeHistory";
import { createRecipeHistory, safeStringifyForHistory } from "./recipe.helper";

// Get models from db object to ensure associations are set up
const Recipe = db.Recipe;

// Constants for batch processing
export const STEPS_BATCH_SIZE = 5;
export const UPLOADS_BATCH_SIZE = 5;

/**
 * Helper function to check if user can create/update recipes
 */
export const canCreateUpdateRecipes = (userRole: any): boolean => {
  if (!userRole) return false;

  const ADMIN_SIDE_USER = ["admin", "chef", "kitchen_manager", "recipe_manager"];
  return ADMIN_SIDE_USER.includes(userRole.role_name);
};

/**
 * Extract common user data from request
 */
export const extractUserData = (req: Request) => {
  const userId = (req as any).user?.id;
  const organizationId = (req as any).user?.organization_id;
  const userRole = (req as any).user?.roles?.[0];

  return { userId, organizationId, userRole };
};

/**
 * Common authorization check for batch operations
 */
export const checkBatchAuthorization = (req: Request, res: Response): { userId: number; organizationId: string; userRole: any } | null => {
  const { userId, organizationId, userRole } = extractUserData(req);

  if (!userId) {
    res.status(StatusCodes.UNAUTHORIZED).json({
      status: false,
      message: "Unauthorized access",
    });
    return null;
  }

  // Permission check: Only certain roles can create/update recipes
  if (!canCreateUpdateRecipes(userRole)) {
    res.status(StatusCodes.FORBIDDEN).json({
      status: false,
      message: "Permission denied",
    });
    return null;
  }

  return { userId, organizationId, userRole };
};

/**
 * Validate that a recipe exists and user has access to it
 */
export const validateRecipeAccess = async (
  recipeId: number,
  organizationId: string,
  transaction?: any
): Promise<any> => {
  const recipe = await Recipe.findOne({
    where: {
      id: recipeId,
      organization_id: organizationId,
      recipe_status: {
        [Op.not]: RecipeStatus.deleted,
      },
    },
    transaction,
  });

  return recipe;
};

/**
 * Common response format for successful batch operations
 */
export const createSuccessResponse = (
  message: string,
  data: any,
  statusCode: number = StatusCodes.OK
) => {
  return {
    status: true,
    message,
    data,
  };
};

/**
 * Common response format for creation operations
 */
export const createCreatedResponse = (message: string, data: any) => {
  return createSuccessResponse(message, data, StatusCodes.CREATED);
};

/**
 * Common validation for required recipe ID
 */
export const validateRecipeId = (recipeId: any, res: Response): boolean => {
  if (!recipeId) {
    res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: "Recipe ID is required",
    });
    return false;
  }

  if (isNaN(parseInt(recipeId))) {
    res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: "Recipe ID must be a valid number",
    });
    return false;
  }

  return true;
};

/**
 * Common validation for required recipe title
 */
export const validateRecipeTitle = (recipeTitle: any, res: Response): boolean => {
  if (!recipeTitle) {
    res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: "Recipe title is required",
    });
    return false;
  }

  return true;
};

/**
 * Common function to update recipe timestamp
 */
export const updateRecipeTimestamp = async (
  recipeId: number,
  userId: number,
  transaction?: any
): Promise<void> => {
  await Recipe.update(
    {
      updated_by: userId,
      updated_at: new Date()
    },
    {
      where: { id: recipeId },
      transaction
    }
  );
};

/**
 * Common function to create history data structure
 */
export const createHistoryData = (
  recipeId: number,
  action: string,
  description: string,
  req: Request,
  organizationId: string,
  userId: number
) => {
  return {
    recipe_id: recipeId,
    action,
    description,
    ip_address: req.ip,
    user_agent: req.get("User-Agent") || "",
    organization_id: organizationId,
    created_by: userId,
  };
};

/**
 * Clean data to prevent circular references before storing in history
 */
const cleanDataForHistory = (data: any, fieldName: string): any => {
  if (data === null || data === undefined) {
    return data;
  }

  // Handle different field types
  switch (fieldName) {
    case 'ingredients':
      if (Array.isArray(data)) {
        // Data should already be properly formatted by formatIngredientsForHistory
        return data.map((ing: any) => ({
          id: ing.id,
          ingredient_name: ing.ingredient_name,
          ingredient_slug: ing.ingredient_slug,
          ingredient_status: ing.ingredient_status,
          ingredient_quantity: ing.ingredient_quantity,
          ingredient_measure: ing.ingredient_measure,
          ingredient_wastage: ing.ingredient_wastage, // Add missing wastage field
          measure_title: ing.measure_title, // Add missing measure_title field
          ingredient_cost: ing.ingredient_cost,
          ingredient_cooking_method: ing.ingredient_cooking_method,
          preparation_method: ing.preparation_method,
          item_detail: ing.item_detail || {}
        }));
      }
      break;

    case 'categories':
    case 'dietary_attributes':
    case 'allergen_attributes':
    case 'cuisine_attributes':
    case 'haccp_attributes':
      if (Array.isArray(data)) {
        return data.map((item: any) => ({
          id: item.id,
          attribute_title: item.attribute_title || item.category_name,
          attribute_slug: item.attribute_slug || item.category_slug,
          attribute_type: item.attribute_type,
          item_detail: item.item_detail && typeof item.item_detail === 'object' ? {
            item_id: item.item_detail.item_id,
            item_type: item.item_detail.item_type,
            item_link: item.item_detail.item_link
          } : {},
          // Add type-specific fields for allergen attributes
          ...(item.may_contain !== undefined && { may_contain: item.may_contain }),
          // Add type-specific fields for HACCP attributes
          ...(item.attribute_description !== undefined && { attribute_description: item.attribute_description }),
          ...(item.use_default !== undefined && { use_default: item.use_default })
        }));
      }
      break;

    case 'nutrition_attributes':
      if (Array.isArray(data)) {
        return data.map((item: any) => ({
          id: item.id,
          attribute_title: item.attribute_title || item.category_name,
          attribute_slug: item.attribute_slug || item.category_slug,
          attribute_type: item.attribute_type,
          item_detail: item.item_detail && typeof item.item_detail === 'object' ? {
            item_id: item.item_detail.item_id,
            item_type: item.item_detail.item_type,
            item_link: item.item_detail.item_link
          } : {},
          // Add nutrition-specific fields for history and highlights
          ...(item.unit !== undefined && { unit: item.unit }),
          ...(item.unit_of_measure !== undefined && { unit_of_measure: item.unit_of_measure }),
          ...(item.attribute_description !== undefined && { attribute_description: item.attribute_description }),
          ...(item.use_default !== undefined && { use_default: item.use_default }),
          ...(item.value !== undefined && { value: item.value }),
          ...(item.daily_value_percentage !== undefined && { daily_value_percentage: item.daily_value_percentage })
        }));
      }
      break;

    default:
      // For primitive types and simple objects, return as-is
      if (typeof data === 'object' && data !== null) {
        // Create a shallow copy to avoid circular references
        try {
          return JSON.parse(JSON.stringify(data));
        } catch (error) {
          // If JSON parsing fails, return a safe representation
          return { _error: 'Could not serialize data', _type: typeof data };
        }
      }
      return data;
  }

  return data;
};

/**
 * Create a consolidated history entry for batch operations
 * This replaces multiple individual field entries with a single comprehensive entry
 */
export const createConsolidatedHistoryEntry = async (
  changedFields: string[],
  oldValues: any,
  newValues: any,
  recipeId: number,
  organizationId: string,
  userId: number,
  req: Request,
  transaction?: any
): Promise<void> => {
  try {
    // Determine the primary action based on the most significant change
    let primaryAction = RecipeHistoryAction.updated;
    let actionPriority = 0;

    // Action priority mapping (higher number = higher priority)
    const actionPriorityMap: { [key: string]: { action: RecipeHistoryAction; priority: number } } = {
      'recipe_status': { action: RecipeHistoryAction.updated, priority: 10 }, // Will be refined below
      'ingredients': { action: RecipeHistoryAction.ingredient_updated, priority: 9 },
      'categories': { action: RecipeHistoryAction.category_added, priority: 8 },
      'nutrition_attributes': { action: RecipeHistoryAction.attribute_added, priority: 7 },
      'allergen_attributes': { action: RecipeHistoryAction.attribute_added, priority: 7 },
      'cuisine_attributes': { action: RecipeHistoryAction.attribute_added, priority: 7 },
      'dietary_attributes': { action: RecipeHistoryAction.attribute_added, priority: 7 },
      'haccp_attributes': { action: RecipeHistoryAction.attribute_added, priority: 7 },
      'is_ingredient_cooking_method': { action: RecipeHistoryAction.ingredient_updated, priority: 6 },
      'is_preparation_method': { action: RecipeHistoryAction.ingredient_updated, priority: 6 },
      'is_cost_manual': { action: RecipeHistoryAction.ingredient_updated, priority: 6 },
      'recipe_title': { action: RecipeHistoryAction.updated, priority: 5 },
      'recipe_placeholder': { action: RecipeHistoryAction.updated, priority: 4 },
    };

    // Find the highest priority action
    for (const field of changedFields) {
      const fieldAction = actionPriorityMap[field];
      if (fieldAction && fieldAction.priority > actionPriority) {
        primaryAction = fieldAction.action;
        actionPriority = fieldAction.priority;

        // Special handling for recipe_status
        if (field === 'recipe_status') {
          const newStatus = newValues[field];
          if (newStatus === 'publish') {
            primaryAction = RecipeHistoryAction.published;
          } else if (newStatus === 'archived') {
            primaryAction = RecipeHistoryAction.archived;
          }
        }
      }
    }

    // Build consolidated description
    const changeDescriptions: string[] = [];
    const oldValuesData: { [key: string]: any } = {};
    const newValuesData: { [key: string]: any } = {};

    for (const field of changedFields) {
      const oldVal = oldValues[field];
      const newVal = newValues[field];

      // Clean data to prevent circular references before storing
      const cleanedOldVal = cleanDataForHistory(oldVal, field);
      const cleanedNewVal = cleanDataForHistory(newVal, field);

      // Store field data in the format expected by highlight parsing logic
      oldValuesData[field] = cleanedOldVal;
      newValuesData[field] = cleanedNewVal;

      // Generate human-readable description for each field
      let fieldDescription = '';
      const fieldDisplayName = field.replace(/_/g, ' ');

      switch (field) {
        case 'recipe_title':
          fieldDescription = `Title: "${oldVal}" → "${newVal}"`;
          break;
        case 'recipe_public_title':
          fieldDescription = `Public title: ${oldVal ? `"${oldVal}"` : 'none'} → "${newVal}"`;
          break;
        case 'recipe_description': {
          const oldDesc = oldVal ? (oldVal.length > 30 ? oldVal.substring(0, 30) + '...' : oldVal) : 'none';
          const newDesc = newVal ? (newVal.length > 30 ? newVal.substring(0, 30) + '...' : newVal) : 'none';
          fieldDescription = `Description: "${oldDesc}" → "${newDesc}"`;
          break;
        }
        case 'recipe_preparation_time':
          fieldDescription = `Prep time: ${oldVal || 0}min → ${newVal || 0}min`;
          break;
        case 'recipe_cook_time':
          fieldDescription = `Cook time: ${oldVal || 0}min → ${newVal || 0}min`;
          break;
        case 'recipe_complexity_level':
          fieldDescription = `Complexity: ${oldVal || 'not set'} → ${newVal}`;
          break;
        case 'recipe_status':
          fieldDescription = `Status: ${oldVal} → ${newVal}`;
          break;
        case 'has_recipe_public_visibility':
          fieldDescription = `Public visibility: ${oldVal ? 'enabled' : 'disabled'} → ${newVal ? 'enabled' : 'disabled'}`;
          break;
        case 'has_recipe_private_visibility':
          fieldDescription = `Private visibility: ${oldVal ? 'enabled' : 'disabled'} → ${newVal ? 'enabled' : 'disabled'}`;
          break;
        case 'categories': {
          const oldCategories = Array.isArray(oldVal) ? oldVal : [];
          const newCategories = Array.isArray(newVal) ? newVal : [];
          fieldDescription = `Categories: ${oldCategories.length} items → ${newCategories.length} items`;
          break;
        }
        case 'dietary_attributes': {
          const oldAttrs = Array.isArray(oldVal) ? oldVal : [];
          const newAttrs = Array.isArray(newVal) ? newVal : [];
          fieldDescription = `Dietary attributes: ${oldAttrs.length} items → ${newAttrs.length} items`;
          break;
        }
        case 'ingredients': {
          const oldIngredients = Array.isArray(oldVal) ? oldVal : [];
          const newIngredients = Array.isArray(newVal) ? newVal : [];

          if (oldIngredients.length === 0) {
            fieldDescription = `Ingredients added: ${newIngredients.length} items`;
          } else if (newIngredients.length === 0) {
            fieldDescription = `All ingredients removed (${oldIngredients.length} items)`;
          } else {
            fieldDescription = `Ingredients: ${oldIngredients.length} items → ${newIngredients.length} items`;
          }
          break;
        }
        case 'nutrition_attributes': {
          const oldAttrs = Array.isArray(oldVal) ? oldVal : [];
          const newAttrs = Array.isArray(newVal) ? newVal : [];
          fieldDescription = `Nutrition attributes: ${oldAttrs.length} items → ${newAttrs.length} items`;
          break;
        }
        case 'allergen_attributes': {
          const oldAttrs = Array.isArray(oldVal) ? oldVal : [];
          const newAttrs = Array.isArray(newVal) ? newVal : [];
          fieldDescription = `Allergen attributes: ${oldAttrs.length} items → ${newAttrs.length} items`;
          break;
        }
        case 'cuisine_attributes': {
          const oldAttrs = Array.isArray(oldVal) ? oldVal : [];
          const newAttrs = Array.isArray(newVal) ? newVal : [];
          fieldDescription = `Cuisine attributes: ${oldAttrs.length} items → ${newAttrs.length} items`;
          break;
        }
        case 'haccp_attributes': {
          const oldAttrs = Array.isArray(oldVal) ? oldVal : [];
          const newAttrs = Array.isArray(newVal) ? newVal : [];
          fieldDescription = `HACCP attributes: ${oldAttrs.length} items → ${newAttrs.length} items`;
          break;
        }
        case 'is_ingredient_cooking_method':
          fieldDescription = `Ingredient cooking method: ${oldVal ? 'enabled' : 'disabled'} → ${newVal ? 'enabled' : 'disabled'}`;
          break;
        case 'is_preparation_method':
          fieldDescription = `Preparation method: ${oldVal ? 'enabled' : 'disabled'} → ${newVal ? 'enabled' : 'disabled'}`;
          break;
        case 'is_cost_manual':
          fieldDescription = `Manual cost: ${oldVal ? 'enabled' : 'disabled'} → ${newVal ? 'enabled' : 'disabled'}`;
          break;
        default:
          fieldDescription = `${fieldDisplayName}: updated`;
      }

      changeDescriptions.push(fieldDescription);
    }

    // Create consolidated description
    const description = `Recipe batch update (${changedFields.length} changes): ${changeDescriptions.join('; ')}`;

    console.log(`DEBUG: Creating history entry with action: ${primaryAction} for fields: ${changedFields.join(', ')}`);

    // Create single history entry with all changes in the correct format for highlight parsing
    await createRecipeHistory({
      recipe_id: recipeId,
      action: primaryAction,
      field_name: 'batch_update',
      old_value: safeStringifyForHistory(oldValuesData),
      new_value: safeStringifyForHistory(newValuesData),
      description,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);

    console.log(`Consolidated history entry created for recipe ${recipeId}: ${changedFields.length} fields updated`);
  } catch (error) {
    console.error("Error creating consolidated history entry:", error);
    // Don't throw to prevent breaking the main operation
  }
};

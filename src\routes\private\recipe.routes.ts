import express from "express";
import uploadService from "../../helper/upload.service";
import recipeController from "../../controller/recipe.controller";
import recipeValidator from "../../validators/recipe.validator";
import batchValidator from "../../validators/batch.validator";
import recipeBatchController from "../../controller/recipe-batch.controller";
import { enhancedUpload } from "../../middleware/upload";
import uploadController from "../../controller/upload.controller";

// Configure S3 Upload for Recipe Files
const multerS3Upload = uploadService.multerS3(
  process.env.NODE_ENV || "development"
);
const router = express.Router();

/**
 * @swagger
 * /private/recipes/create:
 *   post:
 *     tags:
 *       - Recipes
 *     summary: Create a new recipe
 *     description: Create a new recipe with categories, attributes, ingredients, steps, resources, and file uploads (max 10 files + 1 image per step)
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - recipe_title
 *             properties:
 *               recipe_title:
 *                 type: string
 *                 example: "Chicken Alfredo Pasta"
 *                 maxLength: 100
 *               recipe_public_title:
 *                 type: string
 *                 example: "Creamy Chicken Alfredo"
 *                 maxLength: 100
 *               recipe_preparation_time:
 *                 type: integer
 *                 example: 15
 *                 description: "Preparation time in minutes"
 *               recipe_cook_time:
 *                 type: integer
 *                 example: 30
 *                 description: "Cooking time in minutes"
 *               has_recipe_public_visibility:
 *                 type: boolean
 *                 example: false
 *               has_recipe_private_visibility:
 *                 type: boolean
 *                 example: true
 *               recipe_status:
 *                 type: string
 *                 enum: [draft, publish, archived, deleted]
 *                 example: "draft"
 *               recipe_serve_in:
 *                 type: string
 *                 example: "Serve hot with garlic bread"
 *               recipe_garnish:
 *                 type: string
 *                 example: "Fresh parsley and parmesan cheese"
 *               recipe_head_chef_tips:
 *                 type: string
 *                 example: "Use fresh cream for best results"
 *               recipe_foil_tips:
 *                 type: string
 *                 example: "Cover with foil to keep warm"
 *               recipe_impression:
 *                 type: integer
 *                 example: 0
 *               recipe_placeholder:
 *                 type: integer
 *                 example: 1
 *                 description: "ID of placeholder item"
 *               vitamin_a:
 *                 type: number
 *                 example: 150.5
 *                 description: "Vitamin A content in the recipe"
 *               vitamin_c:
 *                 type: number
 *                 example: 25.0
 *                 description: "Vitamin C content in the recipe"
 *               calcium:
 *                 type: number
 *                 example: 200.0
 *                 description: "Calcium content in the recipe"
 *               iron:
 *                 type: number
 *                 example: 5.5
 *                 description: "Iron content in the recipe"
 *               categories:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2]
 *                 description: "Array of category IDs"
 *               attributes:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     unit_of_measure:
 *                       type: integer
 *                       example: 2
 *                     unit:
 *                       type: number
 *                       example: 500
 *                     description:
 *                       type: string
 *                       example: "High in protein"
 *               ingredients:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     quantity:
 *                       type: number
 *                       example: 2.5
 *                     measure:
 *                       type: integer
 *                       example: 1
 *                     wastage:
 *                       type: number
 *                       example: 5.0
 *                     cost:
 *                       type: number
 *                       example: 12.99
 *                     cooking_method:
 *                       type: integer
 *                       example: 3
 *                     preparation_method:
 *                       type: integer
 *                       example: 4
 *               steps:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     order:
 *                       type: integer
 *                       example: 1
 *                     description:
 *                       type: string
 *                       example: "Boil pasta according to package instructions"
 *                     item_id:
 *                       type: integer
 *                       example: 5
 *               resources:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     type:
 *                       type: string
 *                       enum: [item, link]
 *                       example: "link"
 *                     item_id:
 *                       type: string
 *                       example: "123"
 *                     item_link:
 *                       type: string
 *                       example: "https://example.com/recipe-video.mp4"
 *                     item_link_type:
 *                       type: string
 *                       enum: [image, video, pdf, audio, youtube, link]
 *                       example: "video"
 *               recipeFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: "Upload up to 10 recipe files (images, videos, documents)"
 *                 maxItems: 10
 *               stepImages:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: "Upload images for recipe steps (will be matched with step order). Max 20 images."
 *                 maxItems: 20
 *               recipePlaceholder:
 *                 type: string
 *                 format: binary
 *                 description: "Upload recipe placeholder/thumbnail image"
 *     responses:
 *       201:
 *         description: Recipe created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Recipe'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/create",
  multerS3Upload.fields([
    { name: "recipeFiles" },
    { name: "stepImages" },
    { name: "recipePlaceholder", maxCount: 1 },
  ]),
  recipeValidator.createRecipeValidator(),
  recipeController.createRecipe
);

/**
 * @swagger
 * /private/recipes/get-by-id/{id}:
 *   get:
 *     tags:
 *       - Recipes
 *     summary: Get recipe by ID or slug
 *     description: Retrieve a single recipe by its ID or slug with all relations (categories, ingredients, steps, etc.) including freshness indicators for costs and nutrition
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID (numeric) or slug (string)
 *         required: true
 *         schema:
 *           oneOf:
 *             - type: integer
 *               example: 1
 *             - type: string
 *               example: "chicken-alfredo-pasta"
 *     responses:
 *       200:
 *         description: Recipe retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe retrieved successfully"
 *                 data:
 *                   allOf:
 *                     - $ref: '#/components/schemas/Recipe'
 *                     - type: object
 *                       properties:
 *                         highlight:
 *                           $ref: '#/components/schemas/RecipeHighlight'
 *                         hasRecentChanges:
 *                           type: boolean
 *                           description: Whether the recipe has recent changes within the last 7 days
 *                           example: true
 *       404:
 *         description: Recipe not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/get-by-id/:id",
  recipeValidator.getRecipeValidator(),
  recipeController.getRecipeById
);

/**
 * @swagger
 * /private/recipes/list:
 *   get:
 *     tags:
 *       - Recipes
 *     summary: Get recipes list
 *     description: Ultra-optimized recipes list with millisecond response times. Shows public recipes by default, or private recipes when visibility=private. Supports all filters, sorting, and pagination.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - name: search
 *         in: query
 *         description: Search in recipe title, public title, serve in, garnish
 *         required: false
 *         schema:
 *           type: string
 *       - name: categories
 *         in: query
 *         description: Filter by category IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "1,2,3"
 *       - name: allergens
 *         in: query
 *         description: Filter by allergen attribute IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "4,5"
 *       - name: dietary
 *         in: query
 *         description: Filter by dietary attribute IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "6,7"
 *       - name: cuisine
 *         in: query
 *         description: Filter by cuisine attribute IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "8,9"
 *       - name: portion_cost_min
 *         in: query
 *         description: Minimum portion cost filter
 *         required: false
 *         schema:
 *           type: number
 *           minimum: 0
 *       - name: portion_cost_max
 *         in: query
 *         description: Maximum portion cost filter
 *         required: false
 *         schema:
 *           type: number
 *           minimum: 0
 *       - name: bookmark
 *         in: query
 *         description: Filter by bookmark status
 *         required: false
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: visibility
 *         in: query
 *         description: Recipe visibility filter (public by default, private to show private recipes)
 *         required: false
 *         schema:
 *           type: string
 *           enum: [public, private]
 *           default: public
 *       - name: sort_by
 *         in: query
 *         description: Sort field
 *         required: false
 *         schema:
 *           type: string
 *           enum: [alphabetical, title, portion_cost, created_at, updated_at]
 *           default: updated_at
 *       - name: sort_order
 *         in: query
 *         description: Sort order
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *     responses:
 *       200:
 *         description: Recipes retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipes fetched successfully"
 *                 count:
 *                   type: integer
 *                   example: 150
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       recipe_title:
 *                         type: string
 *                         example: "Grilled Chicken Breast"
 *                       recipe_public_title:
 *                         type: string
 *                         example: "Healthy Grilled Chicken"
 *                       description:
 *                         type: string
 *                         example: "Serve with vegetables"
 *                       recipe_status:
 *                         type: string
 *                         example: "publish"
 *                       has_recipe_public_visibility:
 *                         type: boolean
 *                         example: true
 *                       has_recipe_private_visibility:
 *                         type: boolean
 *                         example: true
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                       portion_cost:
 *                         type: number
 *                         example: 12.50
 *                       is_bookmarked:
 *                         type: boolean
 *                         example: false
 *                       main_image:
 *                         type: object
 *                         properties:
 *                           item_id:
 *                             type: integer
 *                           item_type:
 *                             type: string
 *                           item_link:
 *                             type: string
 *                       categories:
 *                         type: string
 *                         example: "Main Course, Healthy"
 *                       allergens:
 *                         type: string
 *                         example: "Dairy, Gluten"
 *                       highlight:
 *                         $ref: '#/components/schemas/RecipeHighlight'
 *                       hasRecentChanges:
 *                         type: boolean
 *                         description: Whether the recipe has recent changes within the last 7 days
 *                         example: true
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 size:
 *                   type: integer
 *                   example: 20
 *                 total_pages:
 *                   type: integer
 *                   example: 8
 *                 response_time_ms:
 *                   type: integer
 *                   example: 45
 *                 highlight_metadata:
 *                   type: object
 *                   properties:
 *                     totalRecipes:
 *                       type: integer
 *                       example: 150
 *                       description: Total number of recipes processed
 *                     recipesWithHighlights:
 *                       type: integer
 *                       example: 23
 *                       description: Number of recipes with highlight information
 *                     queryTimestamp:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-15T10:30:00Z"
 *                       description: When the highlight query was executed
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  "/list",
  recipeValidator.getRecipesListValidator(),
  recipeController.getPrivateRecipesList
);

/**
 * @swagger
 * /private/recipes/update/{id}:
 *   put:
 *     tags:
 *       - Recipes
 *     summary: Update Recipe (Smart Update)
 *     description: Intelligently update recipe - supports both full updates and section-wise updates. Only updates provided fields, perfect for step-wise recipe building.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               recipe_title:
 *                 type: string
 *                 example: "Updated Chicken Alfredo Pasta"
 *                 maxLength: 100
 *               recipe_public_title:
 *                 type: string
 *                 example: "Updated Creamy Chicken Alfredo"
 *                 maxLength: 100
 *               recipe_preparation_time:
 *                 type: integer
 *                 example: 20
 *               recipe_cook_time:
 *                 type: integer
 *                 example: 35
 *               has_recipe_public_visibility:
 *                 type: boolean
 *                 example: true
 *               has_recipe_private_visibility:
 *                 type: boolean
 *                 example: true
 *               recipe_status:
 *                 type: string
 *                 enum: [draft, publish, archived, deleted]
 *                 example: "publish"
 *               recipe_serve_in:
 *                 type: string
 *                 example: "Serve hot with garlic bread and salad"
 *               recipe_garnish:
 *                 type: string
 *                 example: "Fresh parsley, parmesan cheese, and black pepper"
 *               recipe_head_chef_tips:
 *                 type: string
 *                 example: "Use fresh cream and high-quality parmesan for best results"
 *               recipe_foil_tips:
 *                 type: string
 *                 example: "Cover with foil to keep warm during serving"
 *               recipe_impression:
 *                 type: integer
 *                 example: 150
 *               recipe_placeholder:
 *                 type: integer
 *                 example: 2
 *               vitamin_a:
 *                 type: number
 *                 example: 175.0
 *                 description: "Updated Vitamin A content in the recipe"
 *               vitamin_c:
 *                 type: number
 *                 example: 30.0
 *                 description: "Updated Vitamin C content in the recipe"
 *               calcium:
 *                 type: number
 *                 example: 250.0
 *                 description: "Updated Calcium content in the recipe"
 *               iron:
 *                 type: number
 *                 example: 6.0
 *                 description: "Updated Iron content in the recipe"
 *               categories:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 4]
 *               attributes:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     unit_of_measure:
 *                       type: integer
 *                       example: 2
 *                     unit:
 *                       type: number
 *                       example: 600
 *                     description:
 *                       type: string
 *                       example: "High in protein and calcium"
 *               ingredients:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     quantity:
 *                       type: number
 *                       example: 3.0
 *                     measure:
 *                       type: integer
 *                       example: 1
 *                     wastage:
 *                       type: number
 *                       example: 4.0
 *                     cost:
 *                       type: number
 *                       example: 15.99
 *                     cooking_method:
 *                       type: integer
 *                       example: 3
 *                     preparation_method:
 *                       type: integer
 *                       example: 4
 *               steps:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     order:
 *                       type: integer
 *                       example: 1
 *                     description:
 *                       type: string
 *                       example: "Boil pasta in salted water according to package instructions"
 *                     item_id:
 *                       type: integer
 *                       example: 5
 *               resources:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     type:
 *                       type: string
 *                       enum: [item, link]
 *                       example: "link"
 *                     item_id:
 *                       type: string
 *                       example: "456"
 *                     item_link:
 *                       type: string
 *                       example: "https://example.com/updated-recipe-video.mp4"
 *                     item_link_type:
 *                       type: string
 *                       enum: [image, video, pdf, audio, youtube, link]
 *                       example: "video"
 *               recipeFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: "Upload recipe files (videos, PDFs, documents). Max 10 files."
 *                 maxItems: 10
 *               stepImages:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: "Upload images for recipe steps (will be matched with step order). Max 20 images."
 *                 maxItems: 20
 *               recipePlaceholder:
 *                 type: string
 *                 format: binary
 *                 description: "Upload recipe placeholder/thumbnail image"
 *     responses:
 *       200:
 *         description: Recipe updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Recipe'
 *       404:
 *         description: Recipe not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put(
  "/update/:id",
  multerS3Upload.fields([
    { name: "recipeFiles" },
    { name: "stepImages" },
    { name: "recipePlaceholder", maxCount: 1 },
  ]),
  recipeValidator.updateRecipeValidator(),
  recipeController.updateRecipe
);

/**
 * @swagger
 * /private/recipes/archive/{id}:
 *   put:
 *     tags:
 *       - Recipes
 *     summary: Archive Recipe
 *     description: Archives a recipe and removes all user assignments and bookmarks. Only ADMIN_SIDE_USER can archive recipes.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: idJ
 *         in: path
 *         description: Recipe ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       200:
 *         description: Recipe archived successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe archived successfully and all assignments/bookmarks removed"
 *       400:
 *         description: Bad request - Recipe already archived
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Recipe is already archived"
 *       403:
 *         description: Forbidden - Permission denied
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Permission denied"
 *       404:
 *         description: Recipe not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put(
  "/archive/:id",
  recipeValidator.getRecipeValidator(),
  recipeController.archiveRecipe
);

/**
 * @swagger
 * /private/recipes/delete/{id}:
 *   delete:
 *     tags:
 *       - Recipes
 *     summary: Delete Recipe (Conditional)
 *     description: Deletes recipe only if no users are assigned OR if recipe is archived. Only ADMIN_SIDE_USER can delete recipes.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       200:
 *         description: Recipe deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe deleted successfully"
 *       400:
 *         description: Bad request - Recipe has active assignments
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Cannot delete recipe with active user assignments. Please archive the recipe first or remove all assignments."
 *                 active_assignments:
 *                   type: integer
 *                   example: 3
 *       403:
 *         description: Forbidden - Permission denied
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Permission denied"
 *       404:
 *         description: Recipe not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete(
  "/delete/:id",
  recipeValidator.getRecipeValidator(),
  recipeController.deleteRecipe
);

/**
 * @swagger
 * /private/recipes/{id}/publish:
 *   post:
 *     tags:
 *       - Recipes
 *     summary: Publish Recipe
 *     description: Change recipe status from draft to published. Only use when user explicitly chooses to publish.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               has_recipe_public_visibility:
 *                 type: boolean
 *                 description: "Make recipe visible to public"
 *                 example: true
 *               has_recipe_private_visibility:
 *                 type: boolean
 *                 description: "Make recipe visible to organization"
 *                 example: true
 *     responses:
 *       200:
 *         description: Recipe published successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe published successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Recipe'
 */
router.post(
  "/publish/:id",
  recipeValidator.publishRecipeValidator(),
  recipeController.publishRecipe
);

/**
 * @swagger
 * /private/recipes/{id}/make-public:
 *   patch:
 *     tags:
 *       - Recipes
 *     summary: Make Recipe Public
 *     description: Change recipe visibility settings to make it public or private.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               has_recipe_public_visibility:
 *                 type: boolean
 *                 description: "Make recipe visible to public"
 *                 example: true
 *               has_recipe_private_visibility:
 *                 type: boolean
 *                 description: "Make recipe visible to organization"
 *                 example: true
 *             required:
 *               - has_recipe_public_visibility
 *     responses:
 *       200:
 *         description: Recipe visibility updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe visibility updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Recipe'
 */
router.put(
  "/make-public/:id",
  recipeValidator.makeRecipePublicValidator(),
  recipeController.makeRecipePublic
);

/**
 * @swagger
 * /private/recipes/history/{id}:
 *   get:
 *     tags:
 *       - Recipes
 *     summary: Get recipe history
 *     description: Retrieve the complete history of changes made to a recipe
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *       - name: page
 *         in: query
 *         description: Page number for pagination
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - name: action
 *         in: query
 *         description: Filter by action type
 *         required: false
 *         schema:
 *           type: string
 *           enum: [created, updated, deleted, published, archived, restored, ingredient_added, ingredient_removed, ingredient_updated, step_added, step_removed, step_updated, category_added, category_removed, attribute_added, attribute_removed, resource_added, resource_removed, bookmark_added, bookmark_removed]
 *       - name: user_id
 *         in: query
 *         description: Filter by user ID who made the change
 *         required: false
 *         schema:
 *           type: integer
 *       - name: filter
 *         in: query
 *         description: Filter history by type - 'activity' for user activities (bookmarks, creation, etc.) or 'history' for recipe modifications (updates, ingredient changes, etc.)
 *         required: false
 *         schema:
 *           type: string
 *           enum: [activity, history]
 *           example: activity
       - name: categorized
         in: query
         description: Return history in categorized format with separate arrays for added_fields, removed_fields, and updated_fields
         required: false
         schema:
           type: string
           enum: [true, false]
           default: false
           example: true
 *     responses:
 *       200:
 *         description: Recipe history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe history retrieved successfully"
 *                 count:
 *                   type: integer
 *                   example: 15
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/RecipeHistory'
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 size:
 *                   type: integer
 *                   example: 20
 *                 total_pages:
 *                   type: integer
 *                   example: 1
 *       404:
 *         description: Recipe not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/history/:id",
  recipeValidator.getHistoryValidator(),
  recipeController.getRecipeHistory
);

/**
 * @swagger
 * /private/recipes/duplicate/{id}:
 *   post:
 *     tags:
 *       - Recipes
 *     summary: Duplicate recipe
 *     description: Create a copy of an existing recipe with all its relations (categories, ingredients, steps, etc.)
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID to duplicate
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       201:
 *         description: Recipe duplicated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe duplicated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Recipe'
 *       404:
 *         description: Recipe not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/duplicate/:id",
  recipeValidator.duplicateRecipeValidator(),
  recipeController.duplicateRecipe
);

/**
 * @swagger
 * /private/recipes/bookmark/{id}:
 *   post:
 *     tags:
 *       - Recipes
 *     summary: Toggle recipe bookmark
 *     description: Add or remove a recipe bookmark for the current user
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       200:
 *         description: Recipe bookmark updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe bookmarked"
 *                 data:
 *                   type: object
 *                   properties:
 *                     recipe_id:
 *                       type: string
 *                       example: "1"
 *                     is_bookmarked:
 *                       type: boolean
 *                       example: true
 *                     action:
 *                       type: string
 *                       example: "added"
 *       404:
 *         description: Recipe not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/bookmark/:id",
  recipeValidator.addBookmarkValidator(),
  recipeController.toggleRecipeBookmark
);

/**
 * @swagger
 * /private/recipes/export/{id}:
 *   get:
 *     tags:
 *       - Recipes
 *     summary: Export single recipe
 *     description: Export a single recipe to Excel, CSV, or PDF format
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *       - name: format
 *         in: query
 *         description: Export format
 *         required: false
 *         schema:
 *           type: string
 *           enum: [excel, csv, pdf]
 *           default: excel
 *     responses:
 *       200:
 *         description: Recipe exported successfully
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             description: Attachment filename
 *             schema:
 *               type: string
 *               example: "attachment; filename=recipe_export_2024-01-15.xlsx"
 *       404:
 *         description: Recipe not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/export/:id",
  recipeValidator.exportRecipeValidator(),
  recipeController.exportRecipe
);

/**
 * @swagger
 * /private/recipes/{id}/assign-users:
 *   post:
 *     tags:
 *       - Recipes
 *     summary: Assign users to recipe
 *     description: Assign multiple users to a recipe. Only ADMIN_SIDE_USER can assign users.
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Recipe ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 3]
 *                 description: Array of user IDs to assign to the recipe
 *             required:
 *               - user_ids
 *     responses:
 *       200:
 *         description: Users assigned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Users assigned to recipe successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     recipe_id:
 *                       type: string
 *                       example: "1"
 *                     assigned_users:
 *                       type: integer
 *                       example: 3
 *                     new_assignments:
 *                       type: integer
 *                       example: 2
 *                     reactivated_assignments:
 *                       type: integer
 *                       example: 1
 *       400:
 *         description: Cannot assign users to archived recipe
 *       403:
 *         description: Permission denied
 *       404:
 *         description: Recipe not found
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /private/recipes/manage-assignments:
 *   post:
 *     tags:
 *       - Recipes
 *     summary: Manage recipe user assignments
 *     description: |
 *       Single API to manage all recipe user assignments. This API can:
 *       - Assign users to a recipe (replace existing assignments)
 *       - Unassign all users (when user_ids is empty array)
 *       - Recipe must be published and private to assign users
 *       - Prevents duplicate assignments
 *       Only ADMIN_SIDE_USER can manage assignments.
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               recipe_id:
 *                 type: integer
 *                 example: 40
 *                 description: Recipe ID to manage assignments for
 *               user_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [182, 180, 178]
 *                 description: |
 *                   Array of user IDs to assign to the recipe.
 *                   If empty array [], all users will be unassigned from the recipe.
 *                   If contains user IDs, only these users will be assigned (others will be unassigned).
 *             required:
 *               - recipe_id
 *               - user_ids
 *     responses:
 *       200:
 *         description: Recipe assignments managed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe assignments managed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     recipe_id:
 *                       type: integer
 *                       example: 40
 *                     assigned_users:
 *                       type: integer
 *                       example: 3
 *                       description: Number of users currently assigned to the recipe
 *                     operation:
 *                       type: string
 *                       enum: [manage_assignments, unassign_all]
 *                       example: "manage_assignments"
 *                       description: Type of operation performed
 *       400:
 *         description: |
 *           Bad request - Recipe must be published and private, or recipe_id missing
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Recipe must be published to assign users"
 *                 current_status:
 *                   type: string
 *                   example: "draft"
 *       403:
 *         description: Permission denied
 *       404:
 *         description: Recipe not found
 *       500:
 *         description: Internal server error
 */
router.post("/manage-assignments", recipeController.manageRecipeAssignments);

/**
 * @swagger
 * /private/recipes/assigned-to-me:
 *   get:
 *     tags:
 *       - Recipes
 *     summary: Get recipes assigned to current user
 *     description: Retrieve list of recipes assigned to the currently authenticated user with pagination, search, and sorting
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number for pagination
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - name: status
 *         in: query
 *         description: Assignment status filter
 *         required: false
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *           default: active
 *       - name: recipe_status
 *         in: query
 *         description: Recipe status filter
 *         required: false
 *         schema:
 *           type: string
 *           enum: [draft, publish, archived]
 *       - name: search
 *         in: query
 *         description: Search recipes by title or description
 *         required: false
 *         schema:
 *           type: string
 *           maxLength: 100
 *       - name: sort_by
 *         in: query
 *         description: Sort field
 *         required: false
 *         schema:
 *           type: string
 *           enum: [assigned_date, recipe_title, created_at]
 *           default: assigned_date
 *       - name: sort_order
 *         in: query
 *         description: Sort order
 *         required: false
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *     responses:
 *       200:
 *         description: Assigned recipes fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Assigned recipes fetched successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       recipe_id:
 *                         type: integer
 *                         example: 1
 *                       user_id:
 *                         type: integer
 *                         example: 123
 *                       status:
 *                         type: string
 *                         example: "active"
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         description: Assignment date
 *                       recipe:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 1
 *                           recipe_title:
 *                             type: string
 *                             example: "Chocolate Cake"
 *                           recipe_description:
 *                             type: string
 *                             example: "Delicious chocolate cake recipe"
 *                           recipe_status:
 *                             type: string
 *                             example: "publish"
 *                           has_recipe_public_visibility:
 *                             type: boolean
 *                             example: true
 *                           has_recipe_private_visibility:
 *                             type: boolean
 *                             example: true
 *                           recipe_complexity_level:
 *                             type: string
 *                             example: "medium"
 *                           recipe_preparation_time:
 *                             type: integer
 *                             example: 30
 *                           recipe_cook_time:
 *                             type: integer
 *                             example: 45
 *                           created_at:
 *                             type: string
 *                             format: date-time
 *                           updated_at:
 *                             type: string
 *                             format: date-time
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       500:
 *         description: Internal server error
 */
router.get(
  "/assigned-to-me",
  recipeValidator.getAssignedRecipesValidator(),
  recipeController.getAssignedRecipes
);

// Basic recipe information API
router.post(
  "/basic-info",
  batchValidator.validateBasicInfoBatch(),
  recipeBatchController.createRecipeBasicInfo
);

// Ingredients, nutrition, and cuisine data API
router.post(
  "/ingredients-nutrition",
  batchValidator.validateIngredientsNutritionBatch(),
  recipeBatchController.addIngredientsNutritionCuisine
);

// Recipe steps batch API
router.post(
  "/recipe-steps",
  batchValidator.validateStepsBatch(),
  recipeBatchController.addRecipeSteps
);

// Recipe file association API
router.post(
  "/add-recipe-resources",
  batchValidator.validateUploadsBatch(),
  recipeBatchController.addRecipeUploads
);

// Recipe file upload API - handles single key file with dynamic path based on upload type
/**
 * @swagger
 * /private/recipes/upload-files:
 *   post:
 *     tags:
 *       - Recipes
 *     summary: Upload a single recipe file with dynamic path determination
 *     description: Upload a single file and automatically determine the correct storage path based on uploadType from request body
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *               - uploadType
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: "The file to upload"
 *               uploadType:
 *                 type: string
 *                 enum: [recipePlaceholder, recipeFiles, stepImages, recipeVideo, recipeAudio, recipeThumbnail, nutritionLabel]
 *                 description: "Type of upload to determine the correct storage path"
 *                 example: "recipePlaceholder"
 *               recipeId:
 *                 type: integer
 *                 description: "Recipe ID for organizing files (required for most upload types)"
 *                 example: 123
 *               stepNumber:
 *                 type: integer
 *                 description: "Step number for step images (required when uploadType is stepImages)"
 *                 example: 1
 *     responses:
 *       201:
 *         description: File uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recipe file uploaded successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     item_id:
 *                       type: integer
 *                       example: 456
 *                       description: "Unique item ID for the uploaded file"
 *                     original_name:
 *                       type: string
 *                       example: "recipe-image.jpg"
 *                     file_name:
 *                       type: string
 *                       example: "recipe-image_123456.jpg"
 *                     file_size:
 *                       type: integer
 *                       example: 1024000
 *                     mime_type:
 *                       type: string
 *                       example: "image/jpeg"
 *                     file_path:
 *                       type: string
 *                       example: "org123/recipe_images/123/recipe-image_123456.jpg"
 *                     file_url:
 *                       type: string
 *                       example: "https://api.example.com/org123/recipe_images/123/recipe-image_123456.jpg"
 *                     item_type:
 *                       type: string
 *                       example: "image"
 *                     file_hash:
 *                       type: string
 *                       example: "abc123def456"
 *                     upload_type:
 *                       type: string
 *                       example: "recipePlaceholder"
 *                     is_duplicate:
 *                       type: boolean
 *                       example: false
 *                     recipe_id:
 *                       type: integer
 *                       example: 123
 *                     step_number:
 *                       type: integer
 *                       example: 1
 *       400:
 *         description: Bad request - missing required fields or invalid upload type
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  "/upload-recipe-files",
  batchValidator.validateMultipleFilesUpload(),
  uploadService
    .multerS3(process.env.NODE_ENV || "development")
    .upload("file", 5),
  uploadController.uploadMultipleFiles
);

// Bulk delete uploaded files API - for discarding multiple files at once
router.delete(
  "/delete-recipe-resources",
  batchValidator.validateBulkDeleteFiles(),
  uploadController.bulkDeleteUploadedFiles
);

/**
 * @swagger
 * /api/v1/private/recipes/attributes/details:
 *   get:
 *     summary: Get detailed attribute information by IDs
 *     description: Retrieve detailed information about food attributes including names, types, and icons
 *     tags: [Private Recipes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: attribute_ids
 *         required: true
 *         schema:
 *           type: string
 *           example: "17,19,23"
 *         description: Comma-separated list of attribute IDs
 *     responses:
 *       200:
 *         description: Attribute details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Attribute details retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     attributes:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 17
 *                           name:
 *                             type: string
 *                             example: "Vegan"
 *                           slug:
 *                             type: string
 *                             example: "vegan"
 *                           type:
 *                             type: string
 *                             example: "dietary"
 *                           description:
 *                             type: string
 *                             example: "Contains no animal products"
 *                           status:
 *                             type: string
 *                             example: "active"
 *                           icon:
 *                             type: object
 *                             nullable: true
 *                             properties:
 *                               id:
 *                                 type: integer
 *                                 example: 123
 *                               name:
 *                                 type: string
 *                                 example: "vegan-icon.svg"
 *                               url:
 *                                 type: string
 *                                 example: "https://example.com/api/v1/public/user/get-file?location=icons/vegan.svg"
 *                     total:
 *                       type: integer
 *                       example: 3
 *       400:
 *         description: Bad request - Invalid or missing attribute IDs
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/attributes/details", recipeController.getAttributeDetails);

/**
 * @swagger
 * /api/v1/private/recipes/ingredients/details:
 *   get:
 *     summary: Get detailed ingredient information by IDs
 *     description: Retrieve detailed information about ingredients including names, descriptions, and costs
 *     tags: [Private Recipes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: ingredient_ids
 *         required: true
 *         schema:
 *           type: string
 *           example: "123,456,789"
 *         description: Comma-separated list of ingredient IDs
 *     responses:
 *       200:
 *         description: Ingredient details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ingredient details retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     ingredients:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 123
 *                           name:
 *                             type: string
 *                             example: "Organic Tomatoes"
 *                           slug:
 *                             type: string
 *                             example: "organic-tomatoes"
 *                           description:
 *                             type: string
 *                             example: "Fresh organic tomatoes from local farms"
 *                           status:
 *                             type: string
 *                             example: "active"
 *                           waste_percentage:
 *                             type: number
 *                             example: 5.5
 *                           unit_of_measure:
 *                             type: integer
 *                             example: 1
 *                           cost_per_unit:
 *                             type: number
 *                             example: 2.50
 *                           cost_last_updated_at:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-15T10:30:00Z"
 *                     total:
 *                       type: integer
 *                       example: 3
 *       400:
 *         description: Bad request - Invalid or missing ingredient IDs
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/ingredients/details", recipeController.getIngredientDetails);

export default router;

import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import { RecipeStatus } from "../models/Recipe";
import { RecipeAttributesStatus } from "../models/RecipeAttributes";
import { RecipeIngredientsStatus } from "../models/RecipeIngredients";
import { RecipeStepsStatus } from "../models/RecipeSteps";
import { RecipeResourceStatus } from "../models/RecipeResources";
import { RecipeHistoryAction } from "../models/RecipeHistory";
import { generateUniqueSlug } from "../helper/slugGenerator";
import { FoodAttributes } from "../models/FoodAttributes";


// Get models from db object to ensure associations are set up
const Recipe = db.Recipe;
const RecipeCategory = db.RecipeCategory;
const RecipeAttributes = db.RecipeAttributes;
const RecipeIngredients = db.RecipeIngredients;
const RecipeSteps = db.RecipeSteps;
const RecipeResources = db.RecipeResources;
// const RecipeHistory = db.RecipeHistory; // Imported via helper functions


import { createRecipeHistory, safeStringifyForHistory } from "../helper/recipe.helper";
import {
  updateRecipeCostTimestamp,
  updateRecipeNutritionTimestamp,
} from "../helper/timestamp.helper";
import {
  TransactionManager,
  ErrorHandler,
} from "../helper/transaction.helper";
import { ValidationHelper } from "../helper/validation.helper";
import {
  getRecipeHighlight,
  hasRecentChanges,
} from "../helper/recipe-highlight.helper";
import settingsService from "../services/settings.service";
import { getPlatformFromRequest, validateModulePermission } from "../helper/common";
import { MODULE_SLUGS, PERMISSION_TYPES } from "../helper/constant";
import { createConsolidatedHistoryEntry } from "../helper/recipe-batch-common.helper";


// ============================================================================
// CONSTANTS AND SHARED UTILITIES
// ============================================================================

// Constants for batch processing (kept for backward compatibility)
// Note: These constants are currently unused but kept for potential future use

// Helper function to get the proper base URL
const getBaseUrl = (): string => {
  const baseUrl = global.config?.API_BASE_URL;

  if (
    baseUrl &&
    baseUrl.includes("/backend-api/v1/public/user/get-file?location=")
  ) {
    // API_BASE_URL already contains the full endpoint, return base part
    return baseUrl.replace(
      "/backend-api/v1/public/user/get-file?location=",
      ""
    );
  } else {
    // For development or when API_BASE_URL is just the base domain
    return (
      process.env.BASE_URL ||
      process.env.FRONTEND_URL ||
      "https://staging.namastevillage.theeasyaccess.com"
    );
  }
};

// Helper function to capture old recipe data for history tracking
const captureOldRecipeData = async (recipeId: number, transaction: any) => {
  try {
    // Get basic recipe data
    const oldRecipe = await Recipe.findByPk(recipeId, {
      transaction,
      raw: true,
    });

    if (!oldRecipe) {
      return null;
    }

    // Get related data for comprehensive history tracking
    const [ingredients, steps, categories, attributes, resources] = await Promise.all([
      // Get ingredients (only active ones for proper comparison)
      RecipeIngredients.findAll({
        where: {
          recipe_id: recipeId,
          recipe_ingredient_status: RecipeIngredientsStatus.active
        },
        transaction,
        raw: true,
      }),
      // Get steps
      RecipeSteps.findAll({
        where: { recipe_id: recipeId },
        order: [['recipe_step_order', 'ASC']],
        transaction,
        raw: true,
      }),
      // Get categories (only active ones)
      RecipeCategory.findAll({
        where: {
          recipe_id: recipeId,
          status: 'active'
        },
        transaction,
        raw: true,
      }),
      // Get attributes with type information
      db.sequelize.query(`
        SELECT ra.*, fa.attribute_type
        FROM mo_recipe_attributes ra
        LEFT JOIN mo_food_attributes fa ON ra.attributes_id = fa.id
        WHERE ra.recipe_id = :recipeId AND ra.status = 'active'
      `, {
        replacements: { recipeId },
        type: db.sequelize.QueryTypes.SELECT,
        transaction,
      }),
      // Get resources
      RecipeResources.findAll({
        where: { recipe_id: recipeId, status: 'active' },
        transaction,
        raw: true,
      }),
    ]);

    return {
      ...oldRecipe,
      ingredients: ingredients || [],
      steps: steps || [],
      categories: categories || [],
      attributes: attributes || [],
      resources: resources || [],
    };
  } catch (error) {
    console.error("Error capturing old recipe data:", error);
    return null;
  }
};

// Helper function to enhance response with highlight data
const enhanceResponseWithHighlights = async (
  recipeId: number,
  organizationId: string,
  baseResponse: any
) => {
  try {
    // Get organization settings to check if highlights are allowed
    const organizationSettings = await settingsService.getStructuredSettingsByOrganizationId(organizationId);
    const isHighlightedAllowed = organizationSettings.privateRecipeVisibilitySettings.highlightChanges;

    if (!isHighlightedAllowed) {
      return baseResponse;
    }

    // Get highlight information
    const highlightResult = await getRecipeHighlight(recipeId, organizationId);
    const highlight = highlightResult ? highlightResult.highlight : {};
    const hasRecent = await hasRecentChanges(recipeId, organizationId);

    // Enhance response with highlight data
    return {
      ...baseResponse,
      data: {
        ...baseResponse.data,
        highlight,
        hasRecentChanges: hasRecent,
      },
    };
  } catch (error) {
    console.error("Error enhancing response with highlights:", error);
    // Return original response if highlight enhancement fails
    return baseResponse;
  }
};



// Common recipe validation
const validateRecipeAccess = async (recipeId: number, organizationId: string, transaction: any) => {
  return await Recipe.findOne({
    where: {
      id: recipeId,
      organization_id: organizationId,
      recipe_status: {
        [Op.not]: RecipeStatus.deleted,
      },
    },
    transaction,
  });
};

// Helper function to associate uploaded files with a recipe
const associateFilesWithRecipe = async (
  recipeId: number,
  resources: any[], // Can be array of item IDs or resource objects
  organizationId: string,
  userId: number,
  transaction: any
): Promise<{ success: boolean; updatedFiles: number; message: string }> => {
  if (!resources || resources.length === 0) {
    return {
      success: true,
      updatedFiles: 0,
      message: "No resources to associate"
    };
  }

  const recipeResourcesData = [];
  let validResourcesCount = 0;
  let skippedResourcesCount = 0;

  for (const resource of resources) {
    try {
      // Handle different input formats
      let resourceData: any = {};

      // Case 1: Simple item ID (number or string)
      if (typeof resource === 'number' || (typeof resource === 'string' && /^\d+$/.test(resource))) {
        const itemId = typeof resource === 'string' ? parseInt(resource, 10) : resource;
        resourceData = {
          type: "item",
          item_id: itemId,
          item_link: null,
          item_link_type: null
        };
      }
      // Case 2: Resource object with type and item_id
      else if (resource && typeof resource === 'object') {
        resourceData = {
          type: resource.type || "item",
          item_id: resource.item_id || null,
          item_link: resource.item_link || null,
          item_link_type: resource.item_link_type || null
        };
      }
      // Case 3: Invalid resource format
      else {
        skippedResourcesCount++;
        continue;
      }

      // Handle external links
      if (resourceData.type === "link" || resourceData.item_link) {
        // Determine item_link_type for external links
        let itemLinkType = "link";
        if (resourceData.item_link_type) {
          itemLinkType = resourceData.item_link_type;
        } else if (resourceData.item_link) {
          // Auto-detect link type from URL
          const url = resourceData.item_link.toLowerCase();
          if (url.includes("youtube.com")) {
            itemLinkType = "youtube";
          } else if (url.includes("vimeo.com") || url.includes(".mp4")) {
            itemLinkType = "video";
          } else if (url.includes("spotify.com") || url.includes(".mp3") || url.includes(".wav")) {
            itemLinkType = "audio";
          } else if (url.includes(".pdf")) {
            itemLinkType = "pdf";
          } else if (url.includes(".jpg") || url.includes(".png") || url.includes(".gif") || url.includes("imgur.com")) {
            itemLinkType = "image";
          } else if (url.includes(".doc") || url.includes(".txt")) {
            itemLinkType = "text";
          } else {
            itemLinkType = "other";
          }
        }

        recipeResourcesData.push({
          recipe_id: recipeId,
          type: "link",
          item_id: null,
          item_link: resourceData.item_link,
          item_link_type: itemLinkType,
          status: RecipeResourceStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        });
        validResourcesCount++;
      }
      // Handle uploaded files (item type)
      else if (resourceData.type === "item" || resourceData.item_id) {
        // Find the Item that belongs to the user's organization
        const item = await db.Item.findOne({
          where: {
            id: resourceData.item_id,
            item_organization_id: organizationId,
            item_status: "active"
          },
          transaction,
        });

        if (!item) {
          skippedResourcesCount++;
          continue; // Skip invalid items
        }

        // Determine item_link_type based on item type
        let itemLinkType;
        if (item.item_type === "image") {
          itemLinkType = "image";
        } else if (item.item_type === "video") {
          itemLinkType = "video";
        } else if (item.item_type === "audio") {
          itemLinkType = "audio";
        } else if (item.item_type === "pdf") {
          itemLinkType = "pdf";
        } else {
          itemLinkType = "document";
        }

        recipeResourcesData.push({
          recipe_id: recipeId,
          type: "item",
          item_id: item.id,
          item_link: null,
          item_link_type: itemLinkType,
          status: RecipeResourceStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        });
        validResourcesCount++;
      }
    } catch {
      // Log error but continue processing other resources
      skippedResourcesCount++;
      continue;
    }
  }

  if (recipeResourcesData.length === 0) {
    return {
      success: false,
      updatedFiles: 0,
      message: `No valid resources found to associate. Processed: ${validResourcesCount}, Skipped: ${skippedResourcesCount}`
    };
  }

  try {
    // Validate each resource data before creation
    for (const resourceData of recipeResourcesData) {
      // Check for required fields
      if (!resourceData.recipe_id) {
        throw new Error(`Missing recipe_id in resource data: ${JSON.stringify(resourceData)}`);
      }
      if (!resourceData.type) {
        throw new Error(`Missing type in resource data: ${JSON.stringify(resourceData)}`);
      }
      if (!resourceData.status) {
        throw new Error(`Missing status in resource data: ${JSON.stringify(resourceData)}`);
      }
    }

    // Create the RecipeResources records
    const createdResources = await RecipeResources.bulkCreate(recipeResourcesData, { transaction });

    return {
      success: true,
      updatedFiles: createdResources.length,
      message: `Successfully associated ${createdResources.length} resources with recipe. Processed: ${validResourcesCount}, Skipped: ${skippedResourcesCount}`
    };
  } catch (error) {
    return {
      success: false,
      updatedFiles: 0,
      message: `Failed to create recipe resources: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

// ============================================================================
// API 1: BASIC RECIPE INFORMATION
// Handles: basic recipe info, categories, dietary_attributes
// ============================================================================

/**
 * @description Handle the basic information step of recipe creation or update
 * @route POST /api/v1/recipes/batch/basic-info
 * @access Private
 * @functionality Creates new recipe or updates existing recipe with basic info, categories, and dietary attributes
 */
const createRecipeBasicInfo = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract basic recipe information fields
    const {
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility,
      has_recipe_private_visibility,
      recipe_status,
      recipe_complexity_level,
      categories,
      dietary_attributes,
      recipe_id
    } = sanitizedBody;

    const user = (req as any).user;
    const { id: userId, organization_id: organizationId } = user;

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.CREATE,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }

    // Validate required fields
    if (!recipe_title) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Recipe title is required",
      });
    }



    let targetRecipe: any;
    let isUpdate = false;
    let recipe_slug: string;
    let oldRecipeData: any = null;

    // Check if this is an update operation
    if (recipe_id) {
      // UPDATE OPERATION: Validate recipe exists and user has access
      targetRecipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
      if (!targetRecipe) {
        return res.status(StatusCodes.NOT_FOUND).json({
          status: false,
          message: "Recipe not found or access denied",
        });
      }
      isUpdate = true;

      // Capture old recipe data for history tracking
      oldRecipeData = await captureOldRecipeData(recipe_id, transaction);

      if (!oldRecipeData) {
        return res.status(StatusCodes.NOT_FOUND).json({
          status: false,
          message: "Recipe not found for update",
        });
      }

      // For updates, check if slug needs to be regenerated (only if title changed)
      if (targetRecipe.recipe_title !== recipe_title) {
        const checkSlugExists = async (slug: string): Promise<boolean> => {
          const existingRecipe = await Recipe.findOne({
            where: {
              recipe_slug: slug,
              organization_id: organizationId,
              recipe_status: {
                [Op.not]: RecipeStatus.deleted,
              },
              id: {
                [Op.not]: recipe_id, // Exclude current recipe from slug check
              },
            },
            transaction,
          });
          return !!existingRecipe;
        };

        recipe_slug = await generateUniqueSlug(
          recipe_title,
          checkSlugExists,
          {
            maxLength: 25,
            separator: "-",
            lowercase: true,
          }
        );
      } else {
        // Keep existing slug if title hasn't changed
        recipe_slug = targetRecipe.recipe_slug;
      }
    } else {
      // CREATE OPERATION: Generate unique slug from recipe title
      const checkSlugExists = async (slug: string): Promise<boolean> => {
        const existingRecipe = await Recipe.findOne({
          where: {
            recipe_slug: slug,
            organization_id: organizationId,
            recipe_status: {
              [Op.not]: RecipeStatus.deleted,
            },
          },
          transaction,
        });
        return !!existingRecipe;
      };

      recipe_slug = await generateUniqueSlug(
        recipe_title,
        checkSlugExists,
        {
          maxLength: 25,
          separator: "-",
          lowercase: true,
        }
      );
    }

    // Prepare recipe data
    const currentTimestamp = new Date();
    const recipeData: any = {
      updated_by: userId,
    };

    // Only add fields that are actually provided in the request
    if (recipe_title !== undefined) recipeData.recipe_title = recipe_title;
    if (recipe_public_title !== undefined) recipeData.recipe_public_title = recipe_public_title;
    if (recipe_description !== undefined) recipeData.recipe_description = recipe_description;
    if (recipe_preparation_time !== undefined) recipeData.recipe_preparation_time = recipe_preparation_time;
    if (recipe_cook_time !== undefined) recipeData.recipe_cook_time = recipe_cook_time;
    if (has_recipe_public_visibility !== undefined) recipeData.has_recipe_public_visibility = has_recipe_public_visibility;
    if (has_recipe_private_visibility !== undefined) recipeData.has_recipe_private_visibility = has_recipe_private_visibility;
    if (recipe_status !== undefined) recipeData.recipe_status = recipe_status;
    if (recipe_complexity_level !== undefined) recipeData.recipe_complexity_level = recipe_complexity_level;

    // For new recipes, set required defaults
    if (!isUpdate) {
      recipeData.recipe_slug = recipe_slug;
      recipeData.ingredient_costs_updated_at = currentTimestamp;
      recipeData.nutrition_values_updated_at = currentTimestamp;
      recipeData.organization_id = organizationId;
      recipeData.created_by = userId;
      // Set defaults for required fields only for new recipes
      if (recipeData.has_recipe_public_visibility === undefined) recipeData.has_recipe_public_visibility = false;
      if (recipeData.has_recipe_private_visibility === undefined) recipeData.has_recipe_private_visibility = false;
      if (recipeData.recipe_status === undefined) recipeData.recipe_status = RecipeStatus.draft;
    }

    if (isUpdate) {
      // UPDATE OPERATION: Update existing recipe
      await Recipe.update(recipeData, {
        where: { id: recipe_id },
        transaction,
      });
      targetRecipe = { ...targetRecipe.dataValues, ...recipeData, id: recipe_id };
    } else {
      // CREATE OPERATION: Create new recipe
      targetRecipe = await Recipe.create(recipeData, { transaction });
    }

    // Handle categories if provided
    if (categories && Array.isArray(categories) && categories.length > 0) {
      if (isUpdate) {
        // For updates: Set existing categories to inactive first
        await db.RecipeCategory.update(
          {
            status: "inactive",
            updated_by: userId
          },
          {
            where: { recipe_id: targetRecipe.id },
            transaction,
          }
        );
      }

      // Process each category individually to handle existing records
      for (const categoryId of categories) {
        const existingCategory = await db.RecipeCategory.findOne({
          where: {
            recipe_id: targetRecipe.id,
            category_id: categoryId
          },
          transaction
        });

        if (existingCategory) {
          // Update existing record to active
          await db.RecipeCategory.update(
            {
              status: "active",
              updated_by: userId
            },
            {
              where: {
                recipe_id: targetRecipe.id,
                category_id: categoryId
              },
              transaction
            }
          );
        } else {
          // Create new record
          await db.RecipeCategory.create({
            recipe_id: targetRecipe.id,
            category_id: categoryId,
            status: "active",
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          }, { transaction });
        }
      }
    }

    // Handle dietary attributes if provided
    if (dietary_attributes && dietary_attributes.length > 0) {
      if (isUpdate) {
        // For updates: Set existing dietary attributes to inactive first
        // Get dietary attribute IDs to identify which ones to deactivate
        const dietaryAttributeIds = await db.sequelize.query(
          `SELECT id FROM mo_food_attributes WHERE attribute_type = 'dietary'`,
          {
            type: db.sequelize.QueryTypes.SELECT,
            transaction
          }
        ).then((results: any[]) => results.map(r => r.id));

        if (dietaryAttributeIds.length > 0) {
          await RecipeAttributes.update(
            {
              status: RecipeAttributesStatus.inactive,
              updated_by: userId
            },
            {
              where: {
                recipe_id: targetRecipe.id,
                attributes_id: {
                  [Op.in]: dietaryAttributeIds
                }
              },
              transaction
            }
          );
        }
      }

      // Process each dietary attribute individually to handle existing records
      for (const attrId of dietary_attributes) {
        const existingAttribute = await RecipeAttributes.findOne({
          where: {
            recipe_id: targetRecipe.id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false
          },
          transaction
        });

        if (existingAttribute) {
          // Update existing record to active
          await RecipeAttributes.update(
            {
              status: RecipeAttributesStatus.active,
              updated_by: userId
            },
            {
              where: { id: existingAttribute.id },
              transaction
            }
          );
        } else {
          // Create new record
          await RecipeAttributes.create({
            recipe_id: targetRecipe.id,
            attributes_id: attrId,
            unit_of_measure: null,
            unit: null,
            may_contain: false, // Required for unique constraint
            use_default: false, // Required for unique constraint
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            attribute_description: null,
            created_by: userId,
            updated_by: userId,
          }, { transaction });
        }
      }
    }

    // For updates, detect what actually changed
    const changedFields: string[] = [];
    const oldValues: any = {};
    const newValues: any = {};

    if (isUpdate && oldRecipeData) {
      // Simple approach: check each field individually if it's provided in request
      if (recipe_title !== undefined && oldRecipeData.recipe_title !== recipe_title) {
        changedFields.push('recipe_title');
        oldValues.recipe_title = oldRecipeData.recipe_title;
        newValues.recipe_title = recipe_title;
      }

      if (recipe_public_title !== undefined && oldRecipeData.recipe_public_title !== recipe_public_title) {
        changedFields.push('recipe_public_title');
        oldValues.recipe_public_title = oldRecipeData.recipe_public_title;
        newValues.recipe_public_title = recipe_public_title;
      }

      if (recipe_description !== undefined && oldRecipeData.recipe_description !== recipe_description) {
        changedFields.push('recipe_description');
        oldValues.recipe_description = oldRecipeData.recipe_description;
        newValues.recipe_description = recipe_description;
      }

      if (recipe_preparation_time !== undefined && oldRecipeData.recipe_preparation_time !== recipe_preparation_time) {
        changedFields.push('recipe_preparation_time');
        oldValues.recipe_preparation_time = oldRecipeData.recipe_preparation_time;
        newValues.recipe_preparation_time = recipe_preparation_time;
      }

      if (recipe_cook_time !== undefined && oldRecipeData.recipe_cook_time !== recipe_cook_time) {
        changedFields.push('recipe_cook_time');
        oldValues.recipe_cook_time = oldRecipeData.recipe_cook_time;
        newValues.recipe_cook_time = recipe_cook_time;
      }

      // Convert boolean values for comparison to prevent false positives
      const oldPublicVisibility = Boolean(oldRecipeData.has_recipe_public_visibility);
      const newPublicVisibility = Boolean(has_recipe_public_visibility);
      if (has_recipe_public_visibility !== undefined && oldPublicVisibility !== newPublicVisibility) {
        changedFields.push('has_recipe_public_visibility');
        oldValues.has_recipe_public_visibility = oldRecipeData.has_recipe_public_visibility;
        newValues.has_recipe_public_visibility = has_recipe_public_visibility;
      }

      const oldPrivateVisibility = Boolean(oldRecipeData.has_recipe_private_visibility);
      const newPrivateVisibility = Boolean(has_recipe_private_visibility);
      if (has_recipe_private_visibility !== undefined && oldPrivateVisibility !== newPrivateVisibility) {
        changedFields.push('has_recipe_private_visibility');
        oldValues.has_recipe_private_visibility = oldRecipeData.has_recipe_private_visibility;
        newValues.has_recipe_private_visibility = has_recipe_private_visibility;
      }

      // Only detect status change if it's explicitly different
      if (recipe_status !== undefined &&
        oldRecipeData.recipe_status !== recipe_status &&
        recipe_status !== '') { // Ignore empty status values
        changedFields.push('recipe_status');
        oldValues.recipe_status = oldRecipeData.recipe_status;
        newValues.recipe_status = recipe_status;
      }

      if (recipe_complexity_level !== undefined && oldRecipeData.recipe_complexity_level !== recipe_complexity_level) {
        changedFields.push('recipe_complexity_level');
        oldValues.recipe_complexity_level = oldRecipeData.recipe_complexity_level;
        newValues.recipe_complexity_level = recipe_complexity_level;
      }

      // Check recipe_placeholder if provided
      if (req.body.recipe_placeholder !== undefined && oldRecipeData.recipe_placeholder !== req.body.recipe_placeholder) {
        changedFields.push('recipe_placeholder');
        oldValues.recipe_placeholder = oldRecipeData.recipe_placeholder;
        newValues.recipe_placeholder = req.body.recipe_placeholder;
      }

      // Check recipe_slug if provided
      if (req.body.recipe_slug !== undefined && oldRecipeData.recipe_slug !== req.body.recipe_slug) {
        changedFields.push('recipe_slug');
        oldValues.recipe_slug = oldRecipeData.recipe_slug;
        newValues.recipe_slug = req.body.recipe_slug;
      }

      // Check categories if provided
      if (categories && Array.isArray(categories)) {


        // Get old categories with detailed information
        const oldCategoryIds = oldRecipeData.categories
          ? oldRecipeData.categories.map((cat: any) => {
            // Handle different possible field names
            return cat.category_id || cat.id || cat.categoryId;
          }).filter((id: any) => id !== undefined)
          : [];



        const oldCategoriesDetailed = oldCategoryIds.length > 0
          ? await Promise.all(
            oldCategoryIds.map(async (catId: number) => {
              if (!catId || isNaN(catId)) {
                return null;
              }

              const baseUrl = getBaseUrl();
              const catDetails = await db.sequelize.query(`
                  SELECT c.id, c.category_name, c.category_slug, c.category_status,
                         i.id as icon_id, i.item_name as icon_name, i.item_location as icon_location,
                         i.item_mime_type as icon_mime_type,
                         CASE WHEN i.item_location IS NOT NULL
                           THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', i.item_location)
                           ELSE NULL
                         END as icon_url
                  FROM mo_category c
                  LEFT JOIN nv_items i ON c.category_icon = i.id
                  WHERE c.id = :catId
                `, {
                replacements: { catId },
                type: db.sequelize.QueryTypes.SELECT,
                transaction,
              });

              const cat = catDetails[0];

              if (!cat) {
                console.warn(`Category not found for ID ${catId} in old categories history tracking`);
                // Try to get category without status filter as fallback
                const fallbackCatDetails = await db.sequelize.query(`
                  SELECT c.id, c.category_name, c.category_slug, c.category_status
                  FROM mo_category c
                  WHERE c.id = :catId
                `, {
                  replacements: { catId },
                  type: db.sequelize.QueryTypes.SELECT,
                  transaction,
                });

                const fallbackCat = fallbackCatDetails[0];
                if (fallbackCat) {
                  console.log(`Found category ${catId} with status: ${fallbackCat.category_status}`);
                  return {
                    id: fallbackCat.id,
                    category_name: fallbackCat.category_name,
                    category_slug: fallbackCat.category_slug,
                    category_status: fallbackCat.category_status,
                    item_detail: {}
                  };
                } else {
                  console.error(`Category ${catId} not found in database at all`);
                  return {
                    id: catId,
                    category_name: 'Unknown',
                    category_slug: 'unknown',
                    category_status: 'unknown',
                    item_detail: {}
                  };
                }
              }

              return {
                id: cat.id,
                category_name: cat.category_name,
                category_slug: cat.category_slug,
                category_status: cat.category_status,
                item_detail: cat.icon_id ? {
                  item_id: cat.icon_id,
                  item_type: cat.icon_mime_type,
                  item_link: cat.icon_url
                } : {}
              };
            })
          ).then(results => results.filter(cat => cat !== null)) // Filter out null results
          : [];

        // Get new categories with detailed information
        const newCategoriesDetailed = await Promise.all(
          categories.map(async (catId: number) => {
            const baseUrl = getBaseUrl();
            const catDetails = await db.sequelize.query(`
              SELECT c.id, c.category_name, c.category_slug, c.category_status,
                     i.id as icon_id, i.item_name as icon_name, i.item_location as icon_location,
                     i.item_mime_type as icon_mime_type,
                     CASE WHEN i.item_location IS NOT NULL
                       THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', i.item_location)
                       ELSE NULL
                     END as icon_url
              FROM mo_category c
              LEFT JOIN nv_items i ON c.category_icon = i.id
              WHERE c.id = :catId
            `, {
              replacements: { catId },
              type: db.sequelize.QueryTypes.SELECT,
              transaction,
            });
            const cat = catDetails[0];

            if (!cat) {
              console.warn(`Category not found for ID ${catId} in new categories history tracking`);
              // Try to get category without status filter as fallback
              const fallbackCatDetails = await db.sequelize.query(`
                SELECT c.id, c.category_name, c.category_slug, c.category_status
                FROM mo_category c
                WHERE c.id = :catId
              `, {
                replacements: { catId },
                type: db.sequelize.QueryTypes.SELECT,
                transaction,
              });

              const fallbackCat = fallbackCatDetails[0];
              if (fallbackCat) {
                console.log(`Found category ${catId} with status: ${fallbackCat.category_status}`);
                return {
                  id: fallbackCat.id,
                  category_name: fallbackCat.category_name,
                  category_slug: fallbackCat.category_slug,
                  category_status: fallbackCat.category_status,
                  item_detail: {}
                };
              } else {
                console.error(`Category ${catId} not found in database at all`);
                return {
                  id: catId,
                  category_name: 'Unknown',
                  category_slug: 'unknown',
                  category_status: 'unknown',
                  item_detail: {}
                };
              }
            }

            return {
              id: cat.id,
              category_name: cat.category_name,
              category_slug: cat.category_slug,
              category_status: cat.category_status,
              item_detail: cat.icon_id ? {
                item_id: cat.icon_id,
                item_type: cat.icon_mime_type,
                item_link: cat.icon_url
              } : {}
            };
          })
        );

        // Compare arrays by IDs to see if they're different
        const oldCatIds = oldCategoriesDetailed.map((cat: any) => cat.id).sort();
        const newCatIds = [...categories].sort(); // Create a copy to avoid mutating original
        const categoriesChanged = JSON.stringify(oldCatIds) !== JSON.stringify(newCatIds);



        if (categoriesChanged) {
          changedFields.push('categories');
          oldValues.categories = oldCategoriesDetailed;
          newValues.categories = newCategoriesDetailed;
        }
      }

      // Check dietary_attributes if provided
      if (dietary_attributes && Array.isArray(dietary_attributes)) {
        // Get old dietary attributes with detailed information
        const oldDietaryAttributeIds = oldRecipeData.attributes
          ? oldRecipeData.attributes
            .filter((attr: any) => attr.attribute_type === 'dietary' && attr.status === 'active')
            .map((attr: any) => attr.attributes_id)
          : [];

        const oldDietaryAttributesDetailed = oldDietaryAttributeIds.length > 0
          ? await Promise.all(
            oldDietaryAttributeIds.map(async (attrId: number) => {
              const baseUrl = getBaseUrl();
              const attrDetails = await db.sequelize.query(`
                  SELECT fa.id, fa.attribute_title, fa.attribute_slug, fa.attribute_type,
                         i.id as icon_id, i.item_name as icon_name, i.item_location as icon_location,
                         i.item_mime_type as icon_mime_type,
                         CASE WHEN i.item_location IS NOT NULL
                           THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', i.item_location)
                           ELSE NULL
                         END as icon_url
                  FROM mo_food_attributes fa
                  LEFT JOIN nv_items i ON fa.attribute_icon = i.id
                  WHERE fa.id = :attrId AND fa.attribute_status = 'active'
                `, {
                replacements: { attrId },
                type: db.sequelize.QueryTypes.SELECT,
                transaction,
              });
              const attr = attrDetails[0] || { id: attrId, attribute_title: 'Unknown', attribute_slug: 'unknown', attribute_type: 'dietary' };
              return {
                id: attr.id,
                attribute_title: attr.attribute_title,
                attribute_slug: attr.attribute_slug,
                attribute_type: attr.attribute_type,
                item_detail: attr.icon_id ? {
                  item_id: attr.icon_id,
                  item_type: attr.icon_mime_type,
                  item_link: attr.icon_url
                } : {}
              };
            })
          )
          : [];

        // Get new dietary attributes with detailed information
        const newDietaryAttributesDetailed = await Promise.all(
          dietary_attributes.map(async (attrId: number) => {
            const baseUrl = getBaseUrl();
            const attrDetails = await db.sequelize.query(`
              SELECT fa.id, fa.attribute_title, fa.attribute_slug, fa.attribute_type,
                     i.id as icon_id, i.item_name as icon_name, i.item_location as icon_location,
                     i.item_mime_type as icon_mime_type,
                     CASE WHEN i.item_location IS NOT NULL
                       THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', i.item_location)
                       ELSE NULL
                     END as icon_url
              FROM mo_food_attributes fa
              LEFT JOIN nv_items i ON fa.attribute_icon = i.id
              WHERE fa.id = :attrId AND fa.attribute_status = 'active'
            `, {
              replacements: { attrId },
              type: db.sequelize.QueryTypes.SELECT,
              transaction,
            });
            const attr = attrDetails[0] || { id: attrId, attribute_title: 'Unknown', attribute_slug: 'unknown', attribute_type: 'dietary' };
            return {
              id: attr.id,
              attribute_title: attr.attribute_title,
              attribute_slug: attr.attribute_slug,
              attribute_type: attr.attribute_type,
              item_detail: attr.icon_id ? {
                item_id: attr.icon_id,
                item_type: attr.icon_mime_type,
                item_link: attr.icon_url
              } : {}
            };
          })
        );

        // Compare arrays by IDs to see if they're different
        const oldIds = oldDietaryAttributesDetailed.map((attr: any) => attr.id).sort();
        const newIds = dietary_attributes.sort();
        const dietaryAttributesChanged = JSON.stringify(oldIds) !== JSON.stringify(newIds);

        if (dietaryAttributesChanged) {
          changedFields.push('dietary_attributes');
          oldValues.dietary_attributes = oldDietaryAttributesDetailed;
          newValues.dietary_attributes = newDietaryAttributesDetailed;
        }
      }
    } else {
      // For new recipes, capture all provided values
      if (recipe_title !== undefined) newValues.recipe_title = recipe_title;
      if (recipe_public_title !== undefined) newValues.recipe_public_title = recipe_public_title;
      if (recipe_description !== undefined) newValues.recipe_description = recipe_description;
      if (recipe_preparation_time !== undefined) newValues.recipe_preparation_time = recipe_preparation_time;
      if (recipe_cook_time !== undefined) newValues.recipe_cook_time = recipe_cook_time;
      if (has_recipe_public_visibility !== undefined) newValues.has_recipe_public_visibility = has_recipe_public_visibility;
      if (has_recipe_private_visibility !== undefined) newValues.has_recipe_private_visibility = has_recipe_private_visibility;
      if (recipe_status !== undefined) newValues.recipe_status = recipe_status;
      if (recipe_complexity_level !== undefined) newValues.recipe_complexity_level = recipe_complexity_level;
      if (categories && Array.isArray(categories)) newValues.categories = categories;
      if (dietary_attributes && Array.isArray(dietary_attributes)) newValues.dietary_attributes = dietary_attributes;
    }

    // Create specific history entries for different types of changes
    if (!isUpdate) {
      // For new recipes, create a single creation history entry with detailed description
      const creationDetails: string[] = [];
      if (recipe_title) creationDetails.push(`Title: "${recipe_title}"`);
      if (recipe_description) creationDetails.push(`Description: "${recipe_description.substring(0, 100)}${recipe_description.length > 100 ? '...' : ''}"`);
      if (recipe_preparation_time) creationDetails.push(`Prep time: ${recipe_preparation_time} minutes`);
      if (recipe_cook_time) creationDetails.push(`Cook time: ${recipe_cook_time} minutes`);
      if (recipe_complexity_level) creationDetails.push(`Complexity: ${recipe_complexity_level}`);
      if (categories && categories.length > 0) creationDetails.push(`Categories: ${categories.length} assigned`);
      if (dietary_attributes && dietary_attributes.length > 0) creationDetails.push(`Dietary attributes: ${dietary_attributes.length} assigned`);

      const description = `Recipe "${recipe_title}" created with:\n${creationDetails.join(';\n')}.`;

      await createRecipeHistory({
        recipe_id: targetRecipe.id,
        action: RecipeHistoryAction.created,
        field_name: "recipe_created",
        new_value: safeStringifyForHistory(newValues),
        description,
        ip_address: req.ip,
        user_agent: req.get("User-Agent") || "",
        organization_id: organizationId,
        created_by: userId,
      }, transaction);
    } else if (isUpdate && changedFields.length > 0) {
      // More robust filtering: only track changes for fields that were explicitly provided AND actually changed
      const explicitlyProvidedFields = Object.keys(req.body);

      // Additional check: only include fields that have meaningful changes
      const meaningfulChangedFields = changedFields.filter(field => {
        const isExplicitlyProvided = explicitlyProvidedFields.includes(field);
        const oldVal = oldValues[field];
        const newVal = newValues[field];

        // Skip if not explicitly provided
        if (!isExplicitlyProvided) {
          return false;
        }

        // For boolean fields, ensure they're actually different
        if (field.includes('visibility')) {
          const actuallyChanged = Boolean(oldVal) !== Boolean(newVal);
          return actuallyChanged;
        }

        // For status field, ensure it's not just empty/undefined
        if (field === 'recipe_status') {
          const actuallyChanged = oldVal !== newVal && newVal && newVal.trim() !== '';
          return actuallyChanged;
        }

        // For arrays (categories, attributes), ensure they're actually different
        if (Array.isArray(oldVal) || Array.isArray(newVal)) {
          const oldIds = Array.isArray(oldVal) ? oldVal.map((item: any) => item.id || item).sort() : [];
          const newIds = Array.isArray(newVal) ? newVal.map((item: any) => item.id || item).sort() : [];
          const actuallyChanged = JSON.stringify(oldIds) !== JSON.stringify(newIds);
          return actuallyChanged;
        }

        // For other fields, simple comparison
        const actuallyChanged = oldVal !== newVal;
        return actuallyChanged;
      });

      // Skip creating history entries if no meaningful changes
      if (meaningfulChangedFields.length === 0) {
        // Skip history creation but continue with the rest of the function
      } else {
        // Create a single consolidated history entry for all changes in this batch operation
        await createConsolidatedHistoryEntry(
          meaningfulChangedFields,
          oldValues,
          newValues,
          targetRecipe.id,
          organizationId,
          userId,
          req,
          transaction
        );
      }
    }

    await transactionManager.commit();

    const responseStatus = isUpdate ? StatusCodes.OK : StatusCodes.CREATED;
    const responseMessage = isUpdate
      ? "Recipe basic information updated successfully"
      : "Recipe basic information saved successfully";

    // Prepare base response
    const baseResponse = {
      status: true,
      message: responseMessage,
      data: {
        recipe_id: targetRecipe.id,
        recipe_slug: targetRecipe.recipe_slug || recipe_slug,
      },
    };

    // Enhance response with highlight data
    const enhancedResponse = await enhanceResponseWithHighlights(
      targetRecipe.id,
      organizationId,
      baseResponse
    );

    return res.status(responseStatus).json(enhancedResponse);
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error processing recipe basic information"
    );
  }
};

// ============================================================================
// API 2: INGREDIENTS, NUTRITION & SERVING DETAILS
// Handles: ingredients, allergens, nutritions, HACCP data, cuisine attributes, serving details
// ============================================================================

/**
 * Format ingredients to match "get recipe by id" structure for history storage
 */
const formatIngredientsForHistory = async (ingredients: any[], transaction?: any): Promise<any[]> => {
  if (!Array.isArray(ingredients) || ingredients.length === 0) {
    return [];
  }

  return await Promise.all(
    ingredients.map(async (ing: any) => {
      try {
        // Handle different input formats (old data vs new data)
        const ingredientId = ing.ingredient_id || ing.id;
        const quantity = ing.ingredient_quantity || ing.quantity;
        const measure = ing.ingredient_measure || ing.measure;
        const wastage = ing.ingredient_wastage || ing.wastage; // Add missing wastage field
        const cost = ing.ingredient_cost || ing.cost;
        const cookingMethod = ing.ingredient_cooking_method || ing.cooking_method;
        const preparationMethod = ing.preparation_method;

        if (!ingredientId) {
          console.warn('Ingredient missing ID:', ing);
          return {
            id: 0,
            ingredient_name: 'Unknown',
            ingredient_slug: 'unknown',
            ingredient_status: 'unknown',
            ingredient_quantity: quantity || 0,
            ingredient_measure: measure || 0,
            ingredient_wastage: wastage || 0, // Add missing wastage field
            measure_title: null, // Add missing measure_title field
            ingredient_cost: cost || 0,
            ingredient_cooking_method: cookingMethod,
            preparation_method: preparationMethod,
            item_detail: {}
          };
        }

        // Get ingredient details and measure title from database
        const [ingredientDetails, measureDetails] = await Promise.all([
          db.sequelize.query(`
            SELECT i.id, i.ingredient_name, i.ingredient_slug, i.ingredient_status
            FROM mo_ingredients i
            WHERE i.id = :ingredientId AND i.ingredient_status = 'active'
          `, {
            replacements: { ingredientId },
            type: db.sequelize.QueryTypes.SELECT,
            transaction,
          }),
          // Get measure title if measure ID is provided
          measure ? db.sequelize.query(`
            SELECT rm.id, rm.unit_title
            FROM mo_recipe_measure rm
            WHERE rm.id = :measureId AND rm.status = 'active'
          `, {
            replacements: { measureId: measure },
            type: db.sequelize.QueryTypes.SELECT,
            transaction,
          }) : Promise.resolve([])
        ]);

        const ingredient = ingredientDetails[0] || {
          id: ingredientId,
          ingredient_name: 'Unknown',
          ingredient_slug: 'unknown',
          ingredient_status: 'unknown'
        };

        const measureUnit = measureDetails[0] || null;

        // Return in the format that matches "get recipe by id" API (ingredients don't have images)
        const result = {
          id: ingredient.id,
          ingredient_name: ingredient.ingredient_name,
          ingredient_slug: ingredient.ingredient_slug,
          ingredient_status: ingredient.ingredient_status,
          ingredient_quantity: quantity || 0,
          ingredient_measure: measure || 0,
          ingredient_wastage: wastage || 0, // Add missing wastage field
          measure_title: measureUnit ? measureUnit.unit_title : null, // Add missing measure_title field
          ingredient_cost: cost || 0,
          ingredient_cooking_method: cookingMethod,
          preparation_method: preparationMethod,
          item_detail: {} // Ingredients don't have images in this system
        };

        return result;
      } catch (error) {
        console.warn(`Failed to format ingredient for history:`, error);
        return {
          id: ing.ingredient_id || ing.id || 0,
          ingredient_name: 'Unknown (Error)',
          ingredient_slug: 'unknown',
          ingredient_status: 'unknown',
          ingredient_quantity: ing.ingredient_quantity || ing.quantity || 0,
          ingredient_measure: ing.ingredient_measure || ing.measure || 0,
          ingredient_wastage: ing.ingredient_wastage || ing.wastage || 0, // Add missing wastage field
          measure_title: null, // Add missing measure_title field (null for error case)
          ingredient_cost: ing.ingredient_cost || ing.cost || 0,
          ingredient_cooking_method: ing.ingredient_cooking_method || ing.cooking_method,
          preparation_method: ing.preparation_method,
          item_detail: {}
        };
      }
    })
  );
};

/**
 * Get attribute type from field name
 */
const getAttributeTypeFromField = (fieldName: string): string => {
  if (fieldName.includes('nutrition')) return 'nutrition';
  if (fieldName.includes('allergen')) return 'allergen';
  if (fieldName.includes('dietary')) return 'dietary';
  if (fieldName.includes('cuisine')) return 'cuisine';
  if (fieldName.includes('haccp')) return 'haccp_category';
  return 'unknown';
};

/**
 * Format old attributes from captured recipe data for history storage
 * This uses the already captured old data instead of re-querying the database
 */
const formatOldAttributesForHistory = async (oldAttributes: any[], attributeType: string) => {
  if (!oldAttributes || oldAttributes.length === 0) return [];

  // Filter attributes by type and format them for history
  const filteredAttributes = oldAttributes.filter((attr: any) =>
    attr.attribute_type === attributeType && attr.status === 'active'
  );

  return await Promise.all(
    filteredAttributes.map(async (attr: any) => {
      try {
        // Get basic attribute details
        const baseUrl = getBaseUrl();
        const attrDetails = await db.sequelize.query(`
          SELECT fa.id, fa.attribute_title, fa.attribute_slug, fa.attribute_type, fa.attribute_description, fa.attribute_icon,
                 i.id as icon_id, i.item_name as icon_name, i.item_location as icon_location,
                 i.item_mime_type as icon_mime_type,
                 CASE WHEN i.item_location IS NOT NULL
                   THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', i.item_location)
                   ELSE NULL
                 END as icon_url
          FROM mo_food_attributes fa
          LEFT JOIN nv_items i ON fa.attribute_icon = i.id
          WHERE fa.id = :attrId AND fa.attribute_status = 'active'
        `, {
          replacements: { attrId: attr.attributes_id },
          type: db.sequelize.QueryTypes.SELECT,
        });

        const attrDetail = attrDetails[0] || {
          id: attr.attributes_id,
          attribute_title: 'Unknown',
          attribute_slug: 'unknown',
          attribute_type: attributeType,
          attribute_description: null
        };

        // Build response with consistent field names matching get recipe by id API
        const result: any = {
          id: attrDetail.id,
          attribute_title: attrDetail.attribute_title,
          attribute_slug: attrDetail.attribute_slug,
          attribute_type: attrDetail.attribute_type,
          item_detail: attrDetail.icon_id ? {
            item_id: attrDetail.icon_id,
            item_type: attrDetail.icon_mime_type,
            item_link: attrDetail.icon_url
          } : {}
        };

        // Add type-specific fields from the captured old data (this is the key fix!)
        if (attributeType === 'nutrition') {
          result.unit = attr.unit || null;
          result.unit_of_measure = attr.unit_of_measure || null;
          result.attribute_description = attr.attribute_description || attrDetail.attribute_description || null;
          result.use_default = attr.use_default || false;
        } else if (attributeType === 'allergen') {
          result.may_contain = attr.may_contain || false;
        } else if (attributeType === 'haccp_category') {
          result.attribute_description = attr.attribute_description || attrDetail.attribute_description || null;
          result.use_default = attr.use_default || false;
        }

        return result;
      } catch (error) {
        console.warn(`Failed to format old attribute details for ID ${attr.attributes_id}:`, error);
        const errorResult: any = {
          id: attr.attributes_id,
          attribute_title: 'Unknown (Error)',
          attribute_slug: 'unknown',
          attribute_type: attributeType,
          item_detail: {}
        };

        // Add type-specific fields for error cases
        if (attributeType === 'nutrition' || attributeType === 'haccp_category') {
          errorResult.unit = attr.unit || null;
          errorResult.unit_of_measure = attr.unit_of_measure || null;
          errorResult.attribute_description = attr.attribute_description || null;
          errorResult.use_default = attr.use_default || false;
        } else if (attributeType === 'allergen') {
          errorResult.may_contain = attr.may_contain || false;
        }

        return errorResult;
      }
    })
  );
};

/**
 * Format nutrition attributes from request data for history storage
 */
const formatNutritionAttributesFromRequest = async (requestData: any[], attributeType: string) => {
  return await Promise.all(
    requestData.map(async (attr: any) => {
      try {
        // Get basic attribute details
        const attrDetails = await db.sequelize.query(`
          SELECT fa.id, fa.attribute_title, fa.attribute_slug, fa.attribute_type, fa.attribute_description, fa.attribute_icon,
                 i.id as icon_id, i.item_name as icon_name, i.item_location as icon_location,
                 i.item_mime_type as icon_mime_type
          FROM mo_food_attributes fa
          LEFT JOIN nv_items i ON fa.attribute_icon = i.id
          WHERE fa.id = :attrId AND fa.attribute_status = 'active'
        `, {
          replacements: { attrId: attr.id },
          type: db.sequelize.QueryTypes.SELECT,
        });

        const attrInfo = attrDetails[0] || {
          id: attr.id,
          attribute_title: 'Unknown',
          attribute_slug: 'unknown',
          attribute_type: attributeType,
          attribute_description: null
        };

        // Generate icon URL using config API_BASE_URL
        const iconUrl = attrInfo.icon_location
          ? `${global.config?.API_BASE_URL || 'https://staging.namastevillage.theeasyaccess.com/backend-api/v1/public/user/get-file?location='}${attrInfo.icon_location}`
          : null;

        return {
          id: attr.id,
          attribute_title: attrInfo.attribute_title,
          attribute_slug: attrInfo.attribute_slug,
          attribute_type: attributeType,
          item_detail: iconUrl ? {
            item_id: attrInfo.icon_id,
            item_type: attrInfo.icon_mime_type,
            item_link: iconUrl
          } : {},
          unit: attr.unit,
          unit_of_measure: attr.unit_of_measure,
          attribute_description: attr.description || attr.attribute_description,
          use_default: attr.use_default || false
        };
      } catch (error) {
        console.error(`Error formatting nutrition attribute ${attr.id}:`, error);
        return {
          id: attr.id,
          attribute_title: 'Unknown',
          attribute_slug: 'unknown',
          attribute_type: attributeType,
          item_detail: {},
          unit: attr.unit,
          unit_of_measure: attr.unit_of_measure,
          attribute_description: attr.description || attr.attribute_description,
          use_default: attr.use_default || false
        };
      }
    })
  );
};

/**
 * Get detailed attribute information for history storage
 */
const getDetailedAttributesForHistory = async (attributeIds: number[], attributeType: string, recipeId: number, transaction: any) => {
  if (!attributeIds || attributeIds.length === 0) return [];

  return await Promise.all(
    attributeIds.map(async (attrId: number) => {
      try {
        // Get basic attribute details with icon information
        const baseUrl = getBaseUrl();
        const attrDetails = await db.sequelize.query(`
          SELECT fa.id, fa.attribute_title, fa.attribute_slug, fa.attribute_type, fa.attribute_description, fa.attribute_icon,
                 i.id as icon_id, i.item_name as icon_name, i.item_location as icon_location,
                 i.item_mime_type as icon_mime_type,
                 CASE WHEN i.item_location IS NOT NULL
                   THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', i.item_location)
                   ELSE NULL
                 END as icon_url
          FROM mo_food_attributes fa
          LEFT JOIN nv_items i ON fa.attribute_icon = i.id
          WHERE fa.id = :attrId AND fa.attribute_status = 'active'
        `, {
          replacements: { attrId },
          type: db.sequelize.QueryTypes.SELECT,
          transaction,
        });

        const attr = attrDetails[0] || {
          id: attrId,
          attribute_title: 'Unknown',
          attribute_slug: 'unknown',
          attribute_type: attributeType,
          attribute_description: null
        };

        // Get type-specific data from RecipeAttributes junction table
        const recipeAttrDetails = await db.sequelize.query(`
          SELECT unit, unit_of_measure, attribute_description, may_contain, use_default
          FROM mo_recipe_attributes
          WHERE recipe_id = :recipeId AND attributes_id = :attrId AND status = 'active'
          LIMIT 1
        `, {
          replacements: { recipeId, attrId },
          type: db.sequelize.QueryTypes.SELECT,
          transaction,
        });

        // Get the first entry or empty object if none found
        const recipeAttr = recipeAttrDetails[0] || {};

        // Build response with consistent field names matching get recipe by id API
        const result: any = {
          id: attr.id,
          attribute_title: attr.attribute_title,
          attribute_slug: attr.attribute_slug,
          attribute_type: attr.attribute_type,
          item_detail: attr.icon_id ? {
            item_id: attr.icon_id,
            item_type: attr.icon_mime_type,
            item_link: attr.icon_url
          } : {}
        };

        // Add type-specific fields based on attribute type
        if (attributeType === 'nutrition') {
          result.unit = recipeAttr.unit || null;
          result.unit_of_measure = recipeAttr.unit_of_measure || null;
          result.attribute_description = recipeAttr.attribute_description || attr.attribute_description || null;
          result.use_default = recipeAttr.use_default || false;
        } else if (attributeType === 'allergen') {
          result.may_contain = recipeAttr.may_contain || false;
        } else if (attributeType === 'haccp_category') {
          result.attribute_description = recipeAttr.attribute_description || attr.attribute_description || null;
          result.use_default = recipeAttr.use_default || false;
        }

        return result;
      } catch (error) {
        console.warn(`Failed to get attribute details for ID ${attrId}:`, error);
        const errorResult: any = {
          id: attrId,
          attribute_title: 'Unknown (Error)',
          attribute_slug: 'unknown',
          attribute_type: attributeType,
          item_detail: {}
        };

        // Add type-specific fields for error cases
        if (attributeType === 'nutrition' || attributeType === 'haccp_category') {
          errorResult.unit = null;
          errorResult.unit_of_measure = null;
          errorResult.attribute_description = null;
          errorResult.use_default = false;
        } else if (attributeType === 'allergen') {
          errorResult.may_contain = false;
        }

        return errorResult;
      }
    })
  );
};

/**
 * Compare two ingredient arrays to detect changes
 */
const hasIngredientsChanged = (oldIngredients: any[], newIngredients: any[]): boolean => {
  if (!Array.isArray(oldIngredients)) oldIngredients = [];
  if (!Array.isArray(newIngredients)) newIngredients = [];

  // Quick length check
  if (oldIngredients.length !== newIngredients.length) {
    return true;
  }

  // Create normalized arrays for comparison (sort by ID)
  // Handle both old format (ingredient_quantity) and new format (quantity)
  const oldNormalized = oldIngredients
    .map(ing => ({
      id: ing.ingredient_id || ing.id, // Handle both old and new format
      quantity: ing.ingredient_quantity || ing.quantity || 0,
      measure: ing.ingredient_measure || ing.measure || 0,
      cost: ing.ingredient_cost || ing.cost || 0,
      cooking_method: ing.ingredient_cooking_method || ing.cooking_method,
      preparation_method: ing.preparation_method
    }))
    .sort((a, b) => a.id - b.id);

  const newNormalized = newIngredients
    .map(ing => ({
      id: ing.ingredient_id || ing.id, // Handle both old and new format
      quantity: ing.ingredient_quantity || ing.quantity || 0,
      measure: ing.ingredient_measure || ing.measure || 0,
      cost: ing.ingredient_cost || ing.cost || 0,
      cooking_method: ing.ingredient_cooking_method || ing.cooking_method,
      preparation_method: ing.preparation_method
    }))
    .sort((a, b) => a.id - b.id);

  // Deep comparison
  const hasChanged = JSON.stringify(oldNormalized) !== JSON.stringify(newNormalized);

  return hasChanged;
};

/**
 * Get only the changed ingredients for history tracking
 */
const getChangedIngredientsForHistory = (oldIngredients: any[], newIngredients: any[]): {
  oldChanged: any[];
  newChanged: any[];
} => {
  if (!Array.isArray(oldIngredients)) oldIngredients = [];
  if (!Array.isArray(newIngredients)) newIngredients = [];

  const oldChanged: any[] = [];
  const newChanged: any[] = [];

  // If lengths are different, return all (complete replacement)
  if (oldIngredients.length !== newIngredients.length) {
    return {
      oldChanged: oldIngredients,
      newChanged: newIngredients
    };
  }

  // Create maps for efficient lookup
  const oldMap = new Map();
  const newMap = new Map();

  oldIngredients.forEach(ing => {
    const id = ing.ingredient_id || ing.id;
    oldMap.set(id, ing);
  });

  newIngredients.forEach(ing => {
    const id = ing.ingredient_id || ing.id;
    newMap.set(id, ing);
  });

  // Find changed and new ingredients
  for (const newIng of newIngredients) {
    const newId = newIng.ingredient_id || newIng.id;
    const oldIng = oldMap.get(newId);

    if (!oldIng) {
      // New ingredient
      newChanged.push(newIng);
    } else {
      // Check if existing ingredient changed
      const oldNormalized = {
        id: oldIng.ingredient_id || oldIng.id,
        quantity: oldIng.ingredient_quantity || oldIng.quantity || 0,
        measure: oldIng.ingredient_measure || oldIng.measure || 0,
        cost: oldIng.ingredient_cost || oldIng.cost || 0,
        cooking_method: oldIng.ingredient_cooking_method || oldIng.cooking_method,
        preparation_method: oldIng.preparation_method
      };

      const newNormalized = {
        id: newIng.ingredient_id || newIng.id,
        quantity: newIng.ingredient_quantity || newIng.quantity || 0,
        measure: newIng.ingredient_measure || newIng.measure || 0,
        cost: newIng.ingredient_cost || newIng.cost || 0,
        cooking_method: newIng.ingredient_cooking_method || newIng.cooking_method,
        preparation_method: newIng.preparation_method
      };

      if (JSON.stringify(oldNormalized) !== JSON.stringify(newNormalized)) {
        oldChanged.push(oldIng);
        newChanged.push(newIng);
      }
    }
  }

  // Find removed ingredients
  for (const oldIng of oldIngredients) {
    const oldId = oldIng.ingredient_id || oldIng.id;
    if (!newMap.has(oldId)) {
      oldChanged.push(oldIng);
    }
  }

  return {
    oldChanged,
    newChanged
  };
};

/**
 * Compare two attribute arrays to detect changes
 */
const hasAttributesChanged = (oldAttributes: any[], newAttributes: any, attributeType: string): boolean => {
  if (!Array.isArray(oldAttributes)) oldAttributes = [];
  if (!Array.isArray(newAttributes)) newAttributes = [];

  // Extract IDs from both old and new attributes
  // Handle case where attributes are already IDs (numbers) or attribute objects
  const oldIds = oldAttributes.map(attr => {
    if (typeof attr === 'number') return attr;
    return attr.id || attr.attributes_id || attr;
  }).sort();

  const newIds = newAttributes.map((attr: any) => {
    if (typeof attr === 'number') return attr;
    return attr.id || attr.attributes_id || attr;
  }).sort();

  return JSON.stringify(oldIds) !== JSON.stringify(newIds);
};

/**
 * Get only the changed attributes for history tracking
 */
const getChangedAttributesForHistory = async (
  oldAttributeIds: number[],
  newAttributeIds: number[],
  attributeType: string,
  recipeId: number,
  transaction: any,
  oldRecipeData: any,
  requestData?: any[]
): Promise<{
  oldChanged: any[];
  newChanged: any[];
}> => {
  const oldChanged: any[] = [];
  const newChanged: any[] = [];

  // If lengths are different, return all (complete replacement)
  if (oldAttributeIds.length !== newAttributeIds.length) {
    const oldFormatted = await formatOldAttributesForHistory(oldRecipeData?.attributes || [], attributeType);
    const newFormatted = requestData && attributeType === 'nutrition'
      ? await formatNutritionAttributesFromRequest(requestData, attributeType)
      : await getDetailedAttributesForHistory(newAttributeIds, attributeType, recipeId, transaction);
    return {
      oldChanged: oldFormatted,
      newChanged: newFormatted
    };
  }

  // Find added attributes
  const addedIds = newAttributeIds.filter(id => !oldAttributeIds.includes(id));
  if (addedIds.length > 0) {
    const addedFormatted = await getDetailedAttributesForHistory(addedIds, attributeType, recipeId, transaction);
    newChanged.push(...addedFormatted);
  }

  // Find removed attributes
  const removedIds = oldAttributeIds.filter(id => !newAttributeIds.includes(id));
  if (removedIds.length > 0) {
    const removedFormatted = await formatOldAttributesForHistory(
      oldRecipeData?.attributes?.filter((attr: any) =>
        removedIds.includes(attr.attributes_id) && attr.attribute_type === attributeType
      ) || [],
      attributeType
    );
    oldChanged.push(...removedFormatted);
  }

  // For attributes that exist in both, check if values changed (for nutrition and HACCP)
  if (attributeType === 'nutrition' || attributeType === 'haccp_category') {
    const commonIds = oldAttributeIds.filter(id => newAttributeIds.includes(id));

    for (const attrId of commonIds) {
      const oldAttr = oldRecipeData?.attributes?.find((attr: any) =>
        attr.attributes_id === attrId && attr.attribute_type === attributeType
      );

      if (oldAttr) {
        const [oldFormatted] = await formatOldAttributesForHistory([oldAttr], attributeType);

        // For nutrition attributes, use request data instead of database data
        let newFormatted;
        if (requestData && attributeType === 'nutrition') {
          const requestAttr = requestData.find((attr: any) => attr.id === attrId);
          if (requestAttr) {
            [newFormatted] = await formatNutritionAttributesFromRequest([requestAttr], attributeType);
          } else {
            [newFormatted] = await getDetailedAttributesForHistory([attrId], attributeType, recipeId, transaction);
          }
        } else {
          [newFormatted] = await getDetailedAttributesForHistory([attrId], attributeType, recipeId, transaction);
        }

        // Compare the formatted attributes to see if values changed
        const oldValues = {
          unit: oldFormatted?.unit,
          unit_of_measure: oldFormatted?.unit_of_measure,
          attribute_description: oldFormatted?.attribute_description,
          use_default: oldFormatted?.use_default
        };

        const newValues = {
          unit: newFormatted?.unit,
          unit_of_measure: newFormatted?.unit_of_measure,
          attribute_description: newFormatted?.attribute_description,
          use_default: newFormatted?.use_default
        };

        if (JSON.stringify(oldValues) !== JSON.stringify(newValues)) {
          oldChanged.push(oldFormatted);
          newChanged.push(newFormatted);
        }
      }
    }
  }

  return {
    oldChanged,
    newChanged
  };
};

/**
 * Format steps to match "get recipe by id" structure for history storage
 */
const formatStepsForHistory = async (steps: any[], transaction?: any): Promise<any[]> => {
  if (!Array.isArray(steps) || steps.length === 0) {
    return [];
  }

  return await Promise.all(
    steps.map(async (step: any) => {
      try {
        const stepId = step.id;
        const stepOrder = step.recipe_step_order || 0;
        const stepDescription = step.recipe_step_description || '';
        const itemId = step.item_id;

        // Get item details if item_id exists
        let itemDetail = {};
        if (itemId) {
          try {
            const baseUrl = getBaseUrl();
            const itemDetails = await db.sequelize.query(`
              SELECT id, item_name, item_location, item_mime_type,
                     CASE WHEN item_location IS NOT NULL
                       THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', item_location)
                       ELSE NULL
                     END as item_url
              FROM nv_items
              WHERE id = :itemId
            `, {
              replacements: { itemId },
              type: db.sequelize.QueryTypes.SELECT,
              transaction
            });

            if (itemDetails && itemDetails.length > 0) {
              const item = itemDetails[0] as any;
              itemDetail = {
                item_id: item.id,
                item_type: item.item_mime_type,
                item_link: item.item_url
              };
            }
          } catch {
            // If item fetch fails, continue with empty item_detail
          }
        }

        return {
          id: stepId,
          recipe_step_order: stepOrder,
          recipe_step_description: stepDescription,
          item_id: itemId,
          status: step.status || 'active',
          item_detail: itemDetail
        };
      } catch (error) {
        console.warn(`Failed to format step for history:`, error);
        return {
          id: step.id || null,
          recipe_step_order: step.recipe_step_order || step.order || 0,
          recipe_step_description: step.recipe_step_description || step.description || '',
          item_id: step.item_id || null,
          status: step.status || 'active',
          item_detail: {}
        };
      }
    })
  );
};

/**
 * Compare two step arrays to detect changes
 */
const hasStepsChanged = (oldSteps: any[], newSteps: any[]): boolean => {
  if (!Array.isArray(oldSteps)) oldSteps = [];
  if (!Array.isArray(newSteps)) newSteps = [];

  // Quick length check
  if (oldSteps.length !== newSteps.length) {
    return true;
  }

  // Normalize steps for comparison
  const normalizeStep = (step: any) => ({
    order: step.recipe_step_order || 0,
    description: (step.recipe_step_description || '').trim(),
    item_id: step.item_id || null
  });

  const oldNormalized = oldSteps.map(normalizeStep).sort((a, b) => a.order - b.order);
  const newNormalized = newSteps.map(normalizeStep).sort((a, b) => a.order - b.order);

  // Deep comparison
  const hasChanged = JSON.stringify(oldNormalized) !== JSON.stringify(newNormalized);

  return hasChanged;
};

/**
 * Format steps response to match "get recipe by id" structure
 */
const formatStepsResponse = async (steps: any[], transaction?: any): Promise<any[]> => {
  if (!Array.isArray(steps) || steps.length === 0) {
    return [];
  }

  return await Promise.all(
    steps.map(async (step: any) => {
      let itemDetail = {};

      if (step.item_id) {
        try {
          const itemDetails = await db.sequelize.query(`
            SELECT id, item_name, item_location, item_mime_type,
                   CASE WHEN item_location IS NOT NULL
                     THEN CONCAT('${process.env.BASE_URL || 'http://localhost:3000'}/backend-api/v1/public/user/get-file?location=', item_location)
                     ELSE NULL
                   END as item_url
            FROM nv_items
            WHERE id = :itemId
          `, {
            replacements: { itemId: step.item_id },
            type: db.sequelize.QueryTypes.SELECT,
            transaction
          });

          if (itemDetails && itemDetails.length > 0) {
            const item = itemDetails[0] as any;
            itemDetail = {
              item_id: item.id,
              item_type: item.item_mime_type,
              item_link: item.item_url
            };
          }
        } catch {
          // If item fetch fails, continue with empty item_detail
        }
      }

      return {
        id: step.id,
        recipe_step_order: step.recipe_step_order,
        recipe_step_description: step.recipe_step_description,
        item_id: step.item_id,
        status: step.status,
        item_detail: itemDetail
      };
    })
  );
};



/**
 * @description Handle ingredients, nutrition, cuisine type data and serving details for a recipe
 * @route POST /api/v1/recipes/batch/ingredients-nutrition
 * @access Private
 * @functionality Updates recipe with ingredients, nutrition, allergens, cuisine, HACCP data, and serving details
 */
const addIngredientsNutritionCuisine = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract ingredients, nutrition data, and serving details
    const {
      recipe_id,
      ingredients,
      nutrition_attributes,
      allergen_attributes,
      cuisine_attributes,
      dietary_attributes,
      haccp_attributes,
      is_ingredient_cooking_method,
      is_preparation_method,
      is_cost_manual,
      // Serving details
      recipe_serve_in,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_impression,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,
      recipe_status
    } = sanitizedBody;




    const user = (req as any).user;
    const { id: userId, organization_id: organizationId } = user;
    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.EDIT,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }


    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Update basic recipe info first
    await Recipe.update(
      {
        is_ingredient_cooking_method:
          is_ingredient_cooking_method === "true" ||
          is_ingredient_cooking_method === true,
        is_preparation_method:
          is_preparation_method === "true" || is_preparation_method === true,
        is_cost_manual:
          is_cost_manual === "true" || is_cost_manual === true,
        recipe_status: recipe_status ? recipe_status : recipe.recipe_status
      },
      {
        where: { id: recipe_id },
        transaction,
      }
    );

    // Capture old recipe data for history tracking AFTER basic info update but BEFORE ingredients update
    const oldRecipeData = await captureOldRecipeData(recipe_id, transaction);

    // Build request data for change detection (but don't update database yet)
    const requestData: any = {};
    if (ingredients !== undefined) requestData.ingredients = ingredients;
    if (nutrition_attributes !== undefined) requestData.nutrition_attributes = nutrition_attributes;
    if (allergen_attributes !== undefined) requestData.allergen_attributes = allergen_attributes;
    if (cuisine_attributes !== undefined) requestData.cuisine_attributes = cuisine_attributes;
    if (dietary_attributes !== undefined) requestData.dietary_attributes = dietary_attributes;
    if (haccp_attributes !== undefined) requestData.haccp_attributes = haccp_attributes;
    if (is_ingredient_cooking_method !== undefined) requestData.is_ingredient_cooking_method = is_ingredient_cooking_method;
    if (is_preparation_method !== undefined) requestData.is_preparation_method = is_preparation_method;
    if (is_cost_manual !== undefined) requestData.is_cost_manual = is_cost_manual;
    // Add new fields for history/highlight tracking
    if (recipe_serve_in !== undefined) requestData.recipe_serve_in = recipe_serve_in;
    if (recipe_garnish !== undefined) requestData.recipe_garnish = recipe_garnish;
    if (recipe_head_chef_tips !== undefined) requestData.recipe_head_chef_tips = recipe_head_chef_tips;
    if (recipe_foh_tips !== undefined) requestData.recipe_foh_tips = recipe_foh_tips;
    if (recipe_yield !== undefined) requestData.recipe_yield = recipe_yield;
    if (recipe_yield_unit !== undefined) requestData.recipe_yield_unit = recipe_yield_unit;
    if (recipe_total_portions !== undefined) requestData.recipe_total_portions = recipe_total_portions;
    if (recipe_single_portion_size !== undefined) requestData.recipe_single_portion_size = recipe_single_portion_size;
    if (recipe_serving_method !== undefined) requestData.recipe_serving_method = recipe_serving_method;

    // Process each field and detect actual changes
    const changedFields: string[] = [];
    const oldValues: any = {};
    const newValues: any = {};

    for (const field of Object.keys(requestData)) {
      const newVal = (requestData as any)[field];
      // Field is guaranteed to be provided since we only added defined values to requestData
      let actuallyChanged = false;
      let formattedOldVal: any = null;
      let formattedNewVal: any = null;

      // Handle ingredients with proper formatting and change detection
      if (field === 'ingredients') {

        // Format both old and new ingredients for comparison
        const newIngredientsFormatted = await formatIngredientsForHistory(newVal || [], transaction);
        const oldIngredientsFormatted = oldRecipeData
          ? await formatIngredientsForHistory(oldRecipeData.ingredients || [], transaction)
          : [];

        // Get only the changed ingredients for history
        const changedIngredients = getChangedIngredientsForHistory(oldIngredientsFormatted, newIngredientsFormatted);

        actuallyChanged = changedIngredients.oldChanged.length > 0 || changedIngredients.newChanged.length > 0;

        if (actuallyChanged) {
          // Store only the changed ingredients in history
          formattedOldVal = changedIngredients.oldChanged;
          formattedNewVal = changedIngredients.newChanged;
        }

      } else if (field.includes('attributes')) {
        // Handle attribute fields with proper formatting
        const attributeType = getAttributeTypeFromField(field);

        // Get old attribute IDs and format them
        const oldAttributeIds = oldRecipeData && oldRecipeData.attributes
          ? oldRecipeData.attributes
            .filter((attr: any) => attr.attribute_type === attributeType && attr.status === 'active')
            .map((attr: any) => attr.attributes_id)
          : [];

        // Get new attribute IDs based on field type
        let newAttributeIds: number[] = [];
        if (attributeType === 'allergen' && newVal && typeof newVal === 'object' && !Array.isArray(newVal)) {
          // Handle allergen special structure
          newAttributeIds = [
            ...(newVal.contains || []),
            ...(newVal.may_contain || [])
          ];
        } else if (Array.isArray(newVal)) {
          newAttributeIds = newVal.map((attr: any) => attr.id || attr);
        } else if (newVal === null || newVal === undefined) {
          // Treat null/undefined as empty array (remove all)
          newAttributeIds = [];
        }

        // Get only the changed attributes for history
        // For nutrition attributes, pass the actual request data to avoid database timing issues
        const requestDataForHistory = field === 'nutrition_attributes' ? newVal : null;
        const changedAttributes = await getChangedAttributesForHistory(
          oldAttributeIds,
          newAttributeIds,
          attributeType,
          recipe_id,
          transaction,
          oldRecipeData,
          requestDataForHistory
        );

        actuallyChanged = changedAttributes.oldChanged.length > 0 || changedAttributes.newChanged.length > 0;

        if (actuallyChanged) {
          // Store only the changed attributes in history
          formattedOldVal = changedAttributes.oldChanged;
          formattedNewVal = changedAttributes.newChanged;

          oldValues[field] = formattedOldVal;
          newValues[field] = formattedNewVal;
          changedFields.push(field);
        }
        continue;
      } else {
        // Handle other fields (serving details, nutrition values, boolean flags, etc.)
        formattedNewVal = newVal;
        formattedOldVal = oldRecipeData ? oldRecipeData[field] : null;

        // Special handling for boolean fields
        if (field === 'is_ingredient_cooking_method' || field === 'is_preparation_method' || field === 'is_cost_manual') {
          // Normalize boolean values for comparison
          const normalizedOld = formattedOldVal === true || formattedOldVal === 'true' || formattedOldVal === 1;
          const normalizedNew = formattedNewVal === true || formattedNewVal === 'true' || formattedNewVal === 1;

          actuallyChanged = normalizedOld !== normalizedNew;
        } else {
          // Simple comparison for other primitive values
          actuallyChanged = formattedOldVal !== formattedNewVal;
        }
      }

      // Only add to changed fields if actually changed
      if (actuallyChanged) {
        changedFields.push(field);
        oldValues[field] = formattedOldVal;
        newValues[field] = formattedNewVal;
      }
    }

    // Create consolidated history entry for all changes in this operation
    if (changedFields.length > 0) {
      // Create a single consolidated history entry for all changes
      await createConsolidatedHistoryEntry(
        changedFields,
        oldValues,
        newValues,
        recipe_id,
        organizationId,
        userId,
        req,
        transaction
      );

    }

    // NOW perform the actual database updates for ingredients and attributes
    // Handle ingredients if provided (including empty array to clear all ingredients)
    if (ingredients !== undefined) {
      // First, set all existing ingredients to inactive (same approach as regular recipe controller)
      await RecipeIngredients.update(
        {
          recipe_ingredient_status: RecipeIngredientsStatus.inactive,
          updated_by: userId,
        },
        { where: { recipe_id }, transaction }
      );

      // Then create new active ingredients using bulkCreate (only if ingredients array is not empty)
      if (ingredients.length > 0) {
        const ingredientData = ingredients.map((ingredient: any) => ({
          recipe_id,
          ingredient_id: ingredient.id,
          ingredient_quantity: ingredient.quantity,
          ingredient_measure: ingredient.measure,
          ingredient_wastage: ingredient.wastage,
          ingredient_cost: ingredient.cost,
          ingredient_cooking_method: ingredient.cooking_method,
          preparation_method: ingredient.preparation_method,
          recipe_ingredient_status: RecipeIngredientsStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        await RecipeIngredients.bulkCreate(ingredientData, {
          transaction,
          updateOnDuplicate: [
            "recipe_ingredient_status",
            "ingredient_quantity",
            "ingredient_measure",
            "ingredient_wastage",
            "ingredient_cost",
            "ingredient_cooking_method",
            "preparation_method",
            "updated_by",
          ],
        });
      }

      // Update ingredient costs timestamp
      await updateRecipeCostTimestamp(recipe_id, transaction);
    }

    // Add nutrition attributes if provided
    if (
      nutrition_attributes && nutrition_attributes.length > 0 ||
      allergen_attributes ||
      cuisine_attributes ||
      dietary_attributes ||
      haccp_attributes
    ) {
      // Handle nutrition attributes removal (similar to allergen attributes)
      // Process nutrition attributes
      if (nutrition_attributes) {
        // 1. Get all current active nutrition attributes for the recipe
        const currentNutrition = await RecipeAttributes.findAll({
          where: { recipe_id, status: 'active' },
          include: [{
            model: FoodAttributes,
            as: 'attribute', // FIXED alias
            where: { attribute_type: 'nutrition' }
          }],
          transaction,
          raw: true,
        });
        const currentNutritionIds = currentNutrition.map((attr: { attributes_id: number }) => attr.attributes_id);

        // 2. Find which ones are missing in the new request
        const newNutritionIds = nutrition_attributes.map((attr: { id: number }) => attr.id);
        const toRemove = currentNutritionIds.filter((id: number) => !newNutritionIds.includes(id));

        // 3. Set those to inactive
        if (toRemove.length > 0) {
          await RecipeAttributes.update(
            { status: 'inactive', updated_by: userId },
            { where: { recipe_id, attributes_id: toRemove }, transaction }
          );
        }
      }

      if (nutrition_attributes && nutrition_attributes.length > 0) {
        for (const attr of nutrition_attributes) {
          const nutritionData = {
            recipe_id,
            attributes_id: attr.id,
            unit_of_measure: attr.unit_of_measure,
            unit: attr.unit,
            attribute_description: attr.attribute_description,
            use_default: attr.use_default || false,
            may_contain: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(nutritionData, {
            transaction,
            fields: [
              'unit_of_measure', 'unit', 'attribute_description', 'use_default',
              'status', 'updated_by', 'updated_at'
            ]
          });
        }
      }

      // Handle allergen attributes removal (similar to nutrition attributes)
      if (allergen_attributes !== undefined) {
        // 1. Get current allergen attributes for this recipe
        const currentAllergen = await RecipeAttributes.findAll({
          where: {
            recipe_id,
            status: 'active'
          },
          include: [{
            model: FoodAttributes,
            as: 'attribute',
            where: { attribute_type: 'allergen' },
            attributes: ['id', 'attribute_type']
          }],
          attributes: ['attributes_id'],
          transaction,
          raw: true,
        });
        const currentAllergenIds = currentAllergen.map((attr: { attributes_id: number }) => attr.attributes_id);

        // 2. Find which ones are missing in the new request
        const newAllergenIds = [
          ...(allergen_attributes.contains || []),
          ...(allergen_attributes.may_contain || [])
        ];
        const toRemove = currentAllergenIds.filter((id: number) => !newAllergenIds.includes(id));

        // 3. Set those to inactive
        if (toRemove.length > 0) {
          await RecipeAttributes.update(
            { status: 'inactive', updated_by: userId },
            { where: { recipe_id, attributes_id: toRemove }, transaction }
          );
        }
      }

      // Process allergen "contains" attributes
      if (allergen_attributes?.contains && allergen_attributes.contains.length > 0) {
        for (const attrId of allergen_attributes.contains) {
          const allergenData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(allergenData, {
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Process allergen "may contain" attributes
      if (allergen_attributes?.may_contain && allergen_attributes.may_contain.length > 0) {
        for (const attrId of allergen_attributes.may_contain) {
          const allergenData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: true,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(allergenData, {
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Handle cuisine attributes removal (similar to nutrition attributes)
      if (cuisine_attributes !== undefined) {
        // 1. Get current cuisine attributes for this recipe
        const currentCuisine = await RecipeAttributes.findAll({
          where: {
            recipe_id,
            status: 'active'
          },
          include: [{
            model: FoodAttributes,
            as: 'attribute',
            where: { attribute_type: 'cuisine' },
            attributes: ['id', 'attribute_type']
          }],
          attributes: ['attributes_id'],
          transaction,
          raw: true,
        });
        const currentCuisineIds = currentCuisine.map((attr: { attributes_id: number }) => attr.attributes_id);

        // 2. Find which ones are missing in the new request
        const newCuisineIds = cuisine_attributes.map((attrId: number) => attrId);
        const toRemove = currentCuisineIds.filter((id: number) => !newCuisineIds.includes(id));

        // 3. Set those to inactive
        if (toRemove.length > 0) {
          await RecipeAttributes.update(
            { status: 'inactive', updated_by: userId },
            { where: { recipe_id, attributes_id: toRemove }, transaction }
          );
        }
      }

      // Process cuisine attributes
      if (cuisine_attributes && cuisine_attributes.length > 0) {
        for (const attrId of cuisine_attributes) {
          const cuisineData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(cuisineData, {
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Handle dietary attributes removal (similar to nutrition attributes)
      if (dietary_attributes !== undefined) {
        // 1. Get current dietary attributes for this recipe
        const currentDietary = await RecipeAttributes.findAll({
          where: {
            recipe_id,
            status: 'active'
          },
          include: [{
            model: FoodAttributes,
            as: 'attribute',
            where: { attribute_type: 'dietary' },
            attributes: ['id', 'attribute_type']
          }],
          attributes: ['attributes_id'],
          transaction,
          raw: true,
        });
        const currentDietaryIds = currentDietary.map((attr: { attributes_id: number }) => attr.attributes_id);

        // 2. Find which ones are missing in the new request
        const newDietaryIds = dietary_attributes.map((attrId: number) => attrId);
        const toRemove = currentDietaryIds.filter((id: number) => !newDietaryIds.includes(id));

        // 3. Set those to inactive
        if (toRemove.length > 0) {
          await RecipeAttributes.update(
            { status: 'inactive', updated_by: userId },
            { where: { recipe_id, attributes_id: toRemove }, transaction }
          );
        }
      }

      // Process dietary attributes
      if (dietary_attributes && dietary_attributes.length > 0) {
        for (const attrId of dietary_attributes) {
          const dietaryData = {
            recipe_id,
            attributes_id: attrId,
            may_contain: false,
            use_default: false,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            created_by: userId,
            updated_by: userId,
          };

          await RecipeAttributes.upsert(dietaryData, {
            transaction,
            fields: ['status', 'updated_by', 'updated_at']
          });
        }
      }

      // Process HACCP attributes - Allow unlimited entries for same attribute ID
      if (haccp_attributes && haccp_attributes.length > 0) {
        // First, get all HACCP attribute IDs to identify which ones to delete
        const haccpAttributeIds = await db.sequelize.query(
          `SELECT id FROM mo_food_attributes WHERE attribute_type = 'haccp_category'`,
          {
            type: db.sequelize.QueryTypes.SELECT,
            transaction
          }
        ).then((results: any[]) => results.map(r => r.id));

        // Delete existing HACCP attributes for this recipe to allow fresh entries
        if (haccpAttributeIds.length > 0) {
          await RecipeAttributes.destroy({
            where: {
              recipe_id,
              attributes_id: {
                [Op.in]: haccpAttributeIds
              }
            },
            transaction
          });
        }

        // Create all new HACCP attribute entries (including unlimited duplicates)
        const haccpData = haccp_attributes.map((attr: any) => ({
          recipe_id,
          attributes_id: attr.id,
          unit_of_measure: null,
          unit: null,
          attribute_description: attr.attribute_description,
          use_default: attr.use_default || false,
          may_contain: false,
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        // Use bulkCreate to insert all entries (no constraints now)
        await RecipeAttributes.bulkCreate(haccpData, {
          transaction,
        });
      }

      // Update nutrition values timestamp
      await updateRecipeNutritionTimestamp(recipe_id, transaction);
    }

    // Update recipe with serving details
    const updateData: any = {
      updated_by: userId,
      updated_at: new Date()
    };

    // Add serving details if provided
    if (recipe_serve_in !== undefined) updateData.recipe_serve_in = recipe_serve_in;
    if (recipe_garnish !== undefined) updateData.recipe_garnish = recipe_garnish;
    if (recipe_head_chef_tips !== undefined) updateData.recipe_head_chef_tips = recipe_head_chef_tips;
    if (recipe_foh_tips !== undefined) updateData.recipe_foh_tips = recipe_foh_tips;
    if (recipe_impression !== undefined) updateData.recipe_impression = recipe_impression;
    if (recipe_yield !== undefined) updateData.recipe_yield = recipe_yield;
    if (recipe_yield_unit !== undefined) updateData.recipe_yield_unit = recipe_yield_unit;
    if (recipe_total_portions !== undefined) updateData.recipe_total_portions = recipe_total_portions;
    if (recipe_single_portion_size !== undefined) updateData.recipe_single_portion_size = recipe_single_portion_size;
    if (recipe_serving_method !== undefined) updateData.recipe_serving_method = recipe_serving_method;

    await recipe.update(updateData, { transaction });

    await transactionManager.commit();

    // Prepare base response
    const baseResponse = {
      status: true,
      message: "Recipe ingredients, nutrition, cuisine data, and serving details saved successfully",
      data: { recipe_id },
    };

    // Enhance response with highlight data
    const enhancedResponse = await enhanceResponseWithHighlights(
      recipe_id,
      organizationId,
      baseResponse
    );

    return res.status(StatusCodes.OK).json(enhancedResponse);
  } catch (error: unknown) {
    await transactionManager.rollback();

    // Handle specific database constraint errors
    const sqlError = error as any;
    if (sqlError.name === 'SequelizeUniqueConstraintError' ||
      (sqlError.message && sqlError.message.includes('PRIMARY already exists'))) {

      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Duplicate data detected. Please check for duplicate ingredients or attributes.",
        errorType: "DUPLICATE_ERROR",
        error: "Validation error",
      });
    }

    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding ingredients, nutrition data, and serving details"
    );
  }
};

// ============================================================================
// API 4: RECIPE STEPS MANAGEMENT
// Handles: recipe steps creation and updates
// ============================================================================

/**
 * @description Add or update recipe steps
 * @route POST /api/v1/recipes/batch/steps
 * @access Private
 * @functionality Processes all recipe steps at once
 */
const addRecipeSteps = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    // Extract steps data
    const {
      recipe_id,
      recipe_steps,
      recipe_status
    } = sanitizedBody;

    const user = (req as any).user;
    const organizationId = user?.organization_id;
    const userId = user?.id;

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.EDIT,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }


    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Validate steps data
    if (!Array.isArray(recipe_steps) || recipe_steps.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Steps array is required and must contain at least one step",
      });
    }

    // Capture old recipe data for history tracking
    const oldRecipeData = await captureOldRecipeData(recipe_id, transaction);

    // Process steps change detection and history tracking
    const requestData: any = {};
    if (recipe_steps !== undefined) requestData.steps = recipe_steps;

    const changedFields: string[] = [];
    const oldValues: any = {};
    const newValues: any = {};

    // Handle steps with proper formatting and change detection
    if (requestData.steps) {
      // Format both old and new steps for comparison
      const formattedNewVal = await formatStepsForHistory(requestData.steps || [], transaction);
      const formattedOldVal = oldRecipeData?.steps
        ? await formatStepsForHistory(oldRecipeData.steps || [], transaction)
        : [];

      // Compare step arrays
      const actuallyChanged = hasStepsChanged(formattedOldVal, formattedNewVal);

      // Always store the detailed arrays for history
      oldValues.steps = formattedOldVal;
      newValues.steps = formattedNewVal;

      if (actuallyChanged) {
        changedFields.push('steps');
      }
    }

    // Delete existing steps to avoid unique constraint violation
    // This is necessary because of the unique constraint on (recipe_id, recipe_step_order)
    await RecipeSteps.destroy({
      where: { recipe_id },
      transaction,
    });

    // Create new step records with proper field mapping
    const stepsData = recipe_steps.map((step: any, index: number) => ({
      recipe_id,
      recipe_step_order: step.recipe_step_order || index + 1,
      recipe_step_description: step.recipe_step_description || "",
      item_id: step.item_id || null,
      status: RecipeStepsStatus.active,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    }));

    // Bulk create all steps
    const createdSteps = await RecipeSteps.bulkCreate(stepsData, {
      transaction,
      returning: true,
    });

    // Update recipe
    await recipe.update(
      {
        recipe_status: recipe_status ? recipe_status : recipe.recipe_status,
        updated_by: userId,
        updated_at: new Date()
      },
      { transaction }
    );

    // Create consolidated history entry for all changes in this operation
    if (changedFields.length > 0) {
      await createConsolidatedHistoryEntry(
        changedFields,
        oldValues,
        newValues,
        recipe_id,
        organizationId,
        userId,
        req,
        transaction
      );
    }

    await transactionManager.commit();

    const baseResponse = {
      status: true,
      message: "Recipe steps saved successfully",
      data: {
        recipe_id
      }
    };

    // Enhance response with highlight data
    const enhancedResponse = await enhanceResponseWithHighlights(
      recipe_id,
      organizationId,
      baseResponse
    );

    return res.status(StatusCodes.OK).json(enhancedResponse);
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding recipe steps"
    );
  }
};

// ============================================================================
// API 3: RECIPE FILE ASSOCIATION
// Handles: associating uploaded files with recipes
// ============================================================================

/**
 * @description Associate uploaded files with a recipe
 * @route POST /api/v1/recipes/batch/uploads
 * @access Private
 * @functionality Associates pre-uploaded Item records with a recipe by creating RecipeResources records
 */
const addRecipeUploads = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Start transaction
    const transaction = await transactionManager.start();

    // Parse form data
    const recipe_id = req.body.recipe_id;
    const rawRecipeResources = req.body.recipe_resources ? req.body.recipe_resources : [];

    // Separate item resources from link resources
    const itemResources: number[] = [];
    const linkResources: any[] = [];

    rawRecipeResources.forEach((resource: any) => {
      if (typeof resource === 'object') {
        if (resource.type === 'link' || resource.item_link) {
          // This is a link resource
          linkResources.push(resource);
        } else if (resource.item_id) {
          // This is an item resource
          itemResources.push(resource.item_id);
        }
      } else if (typeof resource === 'number') {
        // Simple item ID
        itemResources.push(resource);
      }
    });





    const placeholderItemId = req.body.recipe_placeholder ? req.body.recipe_placeholder : null;
    const recipe_status = req.body.recipe_status ? req.body.recipe_status : null;

    const user = (req as any).user;
    const organizationId = user?.organization_id;
    const userId = user?.id;

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.EDIT,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }

    // Validate recipe_id
    if (!recipe_id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Recipe ID is required",
      });
    }

    // Ensure rawRecipeResources is an array
    if (!Array.isArray(rawRecipeResources)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "recipe_resources must be an array",
      });
    }

    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Capture old recipe data for history tracking
    const oldRecipeData = await captureOldRecipeData(recipe_id, transaction);

    if (itemResources.length > 0 || linkResources.length > 0) {
      // Make all existing resources inactive
      await RecipeResources.update(
        { status: RecipeResourceStatus.inactive },
        {
          where: { recipe_id },
          transaction,
        }
      );

      // Associate all resources (items and links) with recipe
      const associationResult = await associateFilesWithRecipe(
        recipe_id,
        rawRecipeResources, // Pass the complete raw resources
        organizationId,
        userId,
        transaction
      );

      if (!associationResult.success) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: associationResult.message,
        });
      }

      // Update recipe
      await recipe.update(
        {
          recipe_status: recipe_status ? recipe_status : recipe.recipe_status,
          updated_by: userId,
          updated_at: new Date()
        },
        { transaction }
      );

      // Create history entry with detailed tracking (only if resources were actually provided)
      if (itemResources.length > 0 || linkResources.length > 0) {
        // Format old resources with detailed information
        const oldResourcesDetailed = oldRecipeData && oldRecipeData.resources
          ? await Promise.all(
            oldRecipeData.resources.map(async (resource: any) => {
              if (resource.item_id) {
                // Get item details for file resources
                const itemDetails = await db.sequelize.query(`
                    SELECT id, item_name, item_type, item_mime_type, item_size, item_location
                    FROM nv_items
                    WHERE id = :itemId AND item_status = 'active'
                  `, {
                  replacements: { itemId: resource.item_id },
                  type: db.sequelize.QueryTypes.SELECT,
                  transaction,
                });
                const item = itemDetails[0] || {
                  id: resource.item_id,
                  item_name: 'Unknown',
                  item_type: 'unknown',
                  item_mime_type: 'unknown',
                  item_size: 0,
                  item_location: null
                };
                return {
                  id: resource.id,
                  type: resource.type,
                  item_id: resource.item_id,
                  item_name: item.item_name,
                  item_type: item.item_type,
                  item_mime_type: item.item_mime_type,
                  item_size: item.item_size,
                  status: resource.status
                };
              } else {
                // Link resource
                return {
                  id: resource.id,
                  type: resource.type,
                  item_link: resource.item_link,
                  item_link_type: resource.item_link_type,
                  status: resource.status
                };
              }
            })
          )
          : [];

        // Format new resources with detailed information
        const newResourcesDetailed = [];

        // Process item resources
        for (const itemId of itemResources) {
          const itemDetails = await db.sequelize.query(`
            SELECT id, item_name, item_type, item_mime_type, item_size, item_location
            FROM nv_items
            WHERE id = :itemId AND item_status = 'active'
          `, {
            replacements: { itemId },
            type: db.sequelize.QueryTypes.SELECT,
            transaction,
          });

          const item = itemDetails[0] || {
            id: itemId,
            item_name: 'Unknown',
            item_type: 'unknown',
            item_mime_type: 'unknown',
            item_size: 0,
            item_location: null
          };

          newResourcesDetailed.push({
            type: 'item',
            item_id: itemId,
            item_name: item.item_name,
            item_type: item.item_type,
            item_mime_type: item.item_mime_type,
            item_size: item.item_size,
            status: 'active'
          });
        }

        // Process link resources
        for (const linkResource of linkResources) {
          newResourcesDetailed.push({
            type: 'link',
            item_id: null,
            item_link: linkResource.item_link,
            item_link_type: linkResource.item_link_type,
            status: 'active'
          });
        }

        // Generate detailed description for resource changes
        let description = `Recipe "${recipe.recipe_title}" resources updated:\n`;

        if (oldResourcesDetailed.length === 0) {
          const resourceList = newResourcesDetailed.map((res: any) => {
            const size = res.item_size ? ` (${(res.item_size / 1024 / 1024).toFixed(1)}MB)` : '';
            return `${res.item_name}${size}`;
          }).join(', ');
          description += `${newResourcesDetailed.length} resources added: ${resourceList}`;
        } else if (newResourcesDetailed.length === 0) {
          const oldResourceList = oldResourcesDetailed.map((res: any) => res.item_name || 'Unknown').join(', ');
          description += `All resources removed (previously: ${oldResourceList})`;
        } else {
          const oldResourceNames = oldResourcesDetailed.map((res: any) => res.item_name || 'Unknown').join(', ');
          const newResourceNames = newResourcesDetailed.map((res: any) => res.item_name || 'Unknown').join(', ');
          description += `Resources changed from "${oldResourceNames}" to "${newResourceNames}"`;
          description += `\n(${oldResourcesDetailed.length} → ${newResourcesDetailed.length} resources)`;

          // Add file type summary
          const fileTypes = newResourcesDetailed.reduce((types: any, res: any) => {
            const type = res.item_type || 'unknown';
            types[type] = (types[type] || 0) + 1;
            return types;
          }, {});
          const typeSummary = Object.entries(fileTypes).map(([type, count]) => `${count} ${type}`).join(', ');
          description += `\nFile types: ${typeSummary}`;
        }

        try {
          await createRecipeHistory({
            recipe_id,
            action: RecipeHistoryAction.resource_added,
            field_name: "resources",
            old_value: safeStringifyForHistory(oldResourcesDetailed),
            new_value: safeStringifyForHistory(newResourcesDetailed),
            description,
            ip_address: req.ip,
            user_agent: req.get("User-Agent") || "",
            organization_id: organizationId,
            created_by: userId,
          }, transaction);
        } catch (historyError) {
          console.error("Error creating resource history in batch controller:", historyError);
          // Don't throw error to prevent breaking main operation
        }
      }
    }
    if (placeholderItemId) {
      // Validate placeholder item exists and belongs to organization
      const placeholderItem = await db.Item.findOne({
        where: {
          id: placeholderItemId,
          item_organization_id: organizationId,
          item_status: "active"
        },
        transaction,
      });

      if (!placeholderItem) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: "Placeholder item not found or not accessible",
        });
      }

      // Create recipe placeholder
      await Recipe.update({
        recipe_status: recipe_status ? recipe_status : recipe.recipe_status,
        recipe_placeholder: placeholderItemId
      }, {
        where: {
          id: recipe_id
        },
        transaction
      })

    }
    // Commit transaction
    await transactionManager.commit();

    // Prepare base response
    const baseResponse = {
      status: true,
      message: "Recipe files associated successfully",
      data: { recipe_id }
    };

    // Enhance response with highlight data
    const enhancedResponse = await enhanceResponseWithHighlights(
      recipe_id,
      organizationId,
      baseResponse
    );

    return res.status(StatusCodes.OK).json(enhancedResponse);
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error associating recipe files"
    );
  }
};




// ============================================================================
// EXPORTS - FOCUSED BATCH API ENDPOINTS
// ============================================================================

/**
 * Refactored Recipe Batch Controller
 *
 * This controller has been organized into 7 focused API endpoints:
 *
 * 1. createRecipeBasicInfo - API 1: Basic recipe information, categories, dietary attributes
 * 2. addIngredientsNutritionCuisine - API 2: Ingredients, allergens, nutrition, HACCP data, cuisine attributes, serving details
 * 3. addRecipeSteps - API 4: Recipe steps management (simplified)
 * 4. addRecipeUploads - API 3: File association with recipes (simplified)
 * 5. uploadSingleFile - API 5: Single file upload using upload service - creates Item records and returns item_id
 * 6. deleteUploadedFile - API 6: Delete uploaded files by item_id
 * 7. bulkDeleteUploadedFiles - API 7: Bulk delete temporary files (for discard functionality)
 *
 * Key Features Maintained:
 * - All existing middleware (auth, validation, CORS, rate limiting)
 * - Same response formats and error handling patterns
 * - Transaction management and rollback capabilities
 * - File operation tracking and cleanup
 * - Backward compatibility with existing endpoints
 *
 * Improvements:
 * - Simplified APIs without complex batch logic
 * - Better separation of concerns
 * - Shared utility functions for common operations
 * - Consistent authorization and validation patterns
 * - Clear section organization with descriptive headers
 * - Reduced code duplication
 * - Individual file upload and delete capabilities
 * - Simplified file association process
 */
export default {
  createRecipeBasicInfo,
  addIngredientsNutritionCuisine,
  addRecipeSteps,
  addRecipeUploads
};
